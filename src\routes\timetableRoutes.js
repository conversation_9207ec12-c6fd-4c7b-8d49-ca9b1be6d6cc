const express = require('express');
const timetableController = require('../controllers/timetableController');
const { authenticate, authorize, checkSubscription } = require('../middleware/middleware');

const router = express.Router();

// Routes for school timetable management
router.get('/school/:school_id', authenticate, checkSubscription, authorize(['admin', 'super', 'school_admin', 'teacher']), timetableController.getTimetable);
router.get('/school/:school_id/organized', authenticate, checkSubscription, authorize(['admin', 'super', 'school_admin', 'teacher']), timetableController.getOrganizedTimetable);
router.get('/school/:school_id/stats', authenticate, checkSubscription, authorize(['admin', 'super', 'school_admin', 'teacher']), timetableController.getTimetableStats);
router.get('/school/:school_id/teacher/:teacher_id', authenticate, checkSubscription, authorize(['admin', 'super', 'school_admin', 'teacher']), timetableController.getTeacherTimetable);
router.post('/school/:school_id', authenticate, authorize(['admin', 'super', 'school_admin']), timetableController.createScheduleEntry);
router.put('/school/:school_id/schedule/:schedule_id', authenticate, authorize(['admin', 'super', 'school_admin']), timetableController.updateScheduleEntry);

module.exports = router;
