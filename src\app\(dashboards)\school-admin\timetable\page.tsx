"use client";

import { Clock4, Calendar, Users, BookOpen, Filter, Plus, Edit, Trash2 } from "lucide-react";
import SchoolLayout from "@/components/Dashboard/Layouts/SchoolLayout";
import { Suspense, useEffect, useState } from "react";

import CircularLoader from "@/components/widgets/CircularLoader";
import useAuth from "@/app/hooks/useAuth";
import ProtectedRoute from "@/components/utils/ProtectedRoute";
import { motion } from "framer-motion";
import { SchoolAdminTimetableSkeleton } from "@/components/skeletons";
import {
  getOrganizedTimetable,
  getTimetable,
  getTimetableStats,
  createScheduleEntry,
  updateScheduleEntry,
  deleteScheduleEntry,
  TimetableData,
  Period,
  ScheduleEntry,
  TeacherConflictError,
  ConflictDetails
} from "@/app/services/TimetableServices";
import { getClassesBySchool } from "@/app/services/ClassServices";
import { getSubjectsBySchoolId } from "@/app/services/SubjectServices";
import { getTeacherAssignmentsBySchool, TeacherAssignment } from "@/app/services/TeacherAssignmentServices";
import TimetableModal from "@/components/modals/TimetableModal";
import ScheduleConflictModal from "@/components/modals/ScheduleConflictModal";
import ScheduleDeleteModal from "@/components/modals/ScheduleDeleteModal";
import { useToast, ToastContainer } from "@/components/ui/Toast";



const navigation = {
  icon: Clock4,
  baseHref: "/school-admin/timetable",
  title: "Time Table"
};

const DAYS = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday'];

export default function TimetablePage() {
  const { logout, user } = useAuth();
  const { toasts, removeToast, showSuccess, showError } = useToast();

  // State management
  const [timetableData, setTimetableData] = useState<TimetableData>({});
  const [allClassesTimetable, setAllClassesTimetable] = useState<{[className: string]: TimetableData}>({});
  const [periods, setPeriods] = useState<Period[]>([]);
  const [loadingData, setLoadingData] = useState(true);
  const [selectedClass, setSelectedClass] = useState('all_classes');
  const [isExamMode, setIsExamMode] = useState(false);

  // Modal states
  const [isTimetableModalOpen, setIsTimetableModalOpen] = useState(false);
  const [scheduleToEdit, setScheduleToEdit] = useState<any | null>(null);

  // Schedule conflict modal states
  const [isConflictModalOpen, setIsConflictModalOpen] = useState(false);
  const [conflictDetails, setConflictDetails] = useState<ConflictDetails | null>(null);
  const [conflictMessage, setConflictMessage] = useState("");
  const [conflictType, setConflictType] = useState<'teacher' | 'period'>('teacher');
  const [canReplaceConflict, setCanReplaceConflict] = useState(false);
  const [pendingScheduleData, setPendingScheduleData] = useState<any>(null);

  // Delete confirmation modal states
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [scheduleToDelete, setScheduleToDelete] = useState<ScheduleEntry | null>(null);

  // Additional data for forms
  const [classes, setClasses] = useState<any[]>([]);
  const [subjects, setSubjects] = useState<any[]>([]);
  const [teachers, setTeachers] = useState<any[]>([]);
  const [teacherAssignments, setTeacherAssignments] = useState<TeacherAssignment[]>([]);

  // Loading states
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Get school ID from user
  const schoolId = user?.school_ids?.[0] || user?.school_id;

  // Filter timetable data based on exam mode
  const filterTimetableData = (data: TimetableData): TimetableData => {
    const filteredData: TimetableData = {};

    Object.keys(data).forEach(day => {
      filteredData[day] = {};
      Object.keys(data[day]).forEach(period => {
        const entry = data[day][period];
        if (entry) {
          const isExamEntry = entry.schedule_type === 'Exam';
          // Include entry based on current mode
          if ((isExamMode && isExamEntry) || (!isExamMode && !isExamEntry)) {
            filteredData[day][period] = entry;
          }
        }
      });
    });

    return filteredData;
  };

  // Generate teachers list from teacher assignments
  const generateTeachersFromAssignments = (assignments: TeacherAssignment[]): any[] => {
    if (!assignments || assignments.length === 0) {
      return [];
    }

    // Create a map to avoid duplicates and collect unique teachers
    const teacherMap = new Map();

    assignments.forEach(assignment => {
      if (assignment.is_active && assignment.teacher_id) {
        const teacherId = assignment.teacher_id._id;
        if (!teacherMap.has(teacherId)) {
          // Create teacher object compatible with existing teacher structure
          teacherMap.set(teacherId, {
            _id: assignment.teacher_id._id,
            first_name: assignment.teacher_id.first_name,
            last_name: assignment.teacher_id.last_name,
            name: assignment.teacher_id.name,
            email: assignment.teacher_id.email,
            // Add assignment info for easier access
            assignments: []
          });
        }

        // Add this assignment to the teacher's assignments
        teacherMap.get(teacherId).assignments.push({
          class_id: assignment.class_id._id,
          class_name: assignment.class_id.name,
          subjects: assignment.subjects,
          academic_year: assignment.academic_year
        });
      }
    });

    return Array.from(teacherMap.values());
  };

  // Check for teacher assignment overlaps
  const checkTeacherAssignmentOverlap = (scheduleEntry: ScheduleEntry): {
    hasOverlap: boolean;
    assignedTeacher?: string;
    conflictType: 'no_assignment' | 'different_teacher' | 'match' | 'no_teacher_in_schedule';
  } => {
    // If no teacher is assigned in the schedule, check if there should be one
    if (!scheduleEntry.teacher_name) {
      // Find if there's a teacher assignment for this class and subject
      const assignment = teacherAssignments.find(ta =>
        ta.class_id.name === scheduleEntry.class_name &&
        ta.subjects.includes(scheduleEntry.subject_name)
      );

      if (assignment) {
        const teacherName = assignment.teacher_id.first_name && assignment.teacher_id.last_name
          ? `${assignment.teacher_id.first_name} ${assignment.teacher_id.last_name}`
          : assignment.teacher_id.name;

        return {
          hasOverlap: true,
          assignedTeacher: teacherName,
          conflictType: 'no_assignment'
        };
      }

      return {
        hasOverlap: false,
        conflictType: 'no_teacher_in_schedule'
      };
    }

    // If there's a teacher in the schedule, check if it matches the assignment
    const assignment = teacherAssignments.find(ta =>
      ta.class_id.name === scheduleEntry.class_name &&
      ta.subjects.includes(scheduleEntry.subject_name)
    );

    if (!assignment) {
      return {
        hasOverlap: false,
        conflictType: 'match' // No specific assignment, so current teacher is fine
      };
    }

    const assignedTeacherName = assignment.teacher_id.first_name && assignment.teacher_id.last_name
      ? `${assignment.teacher_id.first_name} ${assignment.teacher_id.last_name}`
      : assignment.teacher_id.name;

    if (scheduleEntry.teacher_name !== assignedTeacherName) {
      return {
        hasOverlap: true,
        assignedTeacher: assignedTeacherName,
        conflictType: 'different_teacher'
      };
    }

    return {
      hasOverlap: false,
      conflictType: 'match'
    };
  };

  // Fetch timetable data from API
  useEffect(() => {
    const fetchTimetableData = async () => {
      if (!schoolId) {
        showError("Error", "No school ID found");
        setLoadingData(false);
        return;
      }

      try {
        setLoadingData(true);

        if (selectedClass === 'all_classes') {
          // Fetch timetables for all classes
          const allClassesResponse = await getOrganizedTimetable(schoolId as string, {});
          console.log("All Classes Timetable Data:", allClassesResponse.timetable);

          // Group timetable data by class
          const classGroupedTimetables: {[className: string]: TimetableData} = {};

          // Get all schedule entries and group by class
          const scheduleResponse = await getTimetable(schoolId as string, {});
          const scheduleEntries = scheduleResponse.schedule_records;

          // Group entries by class name, filtering by exam mode
          scheduleEntries.forEach((entry: ScheduleEntry) => {
            // Filter based on exam mode
            const isExamEntry = entry.schedule_type === 'Exam';
            if (isExamMode && !isExamEntry) return; // Skip non-exam entries in exam mode
            if (!isExamMode && isExamEntry) return; // Skip exam entries in normal mode

            if (!classGroupedTimetables[entry.class_name]) {
              classGroupedTimetables[entry.class_name] = {};
              DAYS.forEach(day => {
                classGroupedTimetables[entry.class_name][day] = {};
              });
            }

            if (!classGroupedTimetables[entry.class_name][entry.day_of_week]) {
              classGroupedTimetables[entry.class_name][entry.day_of_week] = {};
            }

            classGroupedTimetables[entry.class_name][entry.day_of_week][entry.period_number] = entry;
          });

          setAllClassesTimetable(classGroupedTimetables);

          // Filter the main timetable data based on exam mode
          const filteredMainTimetable = filterTimetableData(allClassesResponse.timetable);
          setTimetableData(filteredMainTimetable);
          setPeriods(allClassesResponse.periods);
        } else {
          // Build filters for specific class
          const filters: any = {};
          if (selectedClass !== 'all_classes') filters.class_id = selectedClass;

          // Fetch organized timetable for specific class
          const response = await getOrganizedTimetable(schoolId as string, filters);
          console.log("Timetable Data:", response.timetable);

          // Filter the timetable data based on exam mode
          const filteredTimetable = filterTimetableData(response.timetable);
          setTimetableData(filteredTimetable);
          setPeriods(response.periods);
        }
      } catch (error) {
        console.error("Error fetching timetable data:", error);
        showError("Error", "Failed to load timetable data");
      } finally {
        setLoadingData(false);
      }
    };

    fetchTimetableData();
  }, [schoolId, selectedClass, isExamMode]);

  // Fetch additional data for modals
  useEffect(() => {
    const fetchAdditionalData = async () => {
      if (!schoolId) return;
      console.log("Fetching additional data for school ID:", schoolId);
      try {
        // Fetch data with individual error handling
        const results = await Promise.allSettled([
          getClassesBySchool(schoolId as string),
          getSubjectsBySchoolId(schoolId as string),
          getTeacherAssignmentsBySchool(schoolId as string)
        ]);

        // Handle each result individually
        if (results[0].status === 'fulfilled') {
          setClasses(results[0].value);
        } else {
          console.error("Failed to fetch classes:", results[0].reason);
          setClasses([]);
        }

        if (results[1].status === 'fulfilled') {
          setSubjects(results[1].value);
        } else {
          console.error("Failed to fetch subjects:", results[1].reason);
          setSubjects([]);
        }

        if (results[2].status === 'fulfilled') {
          const assignments = results[2].value.assignments;
          setTeacherAssignments(assignments);

          // Generate teachers list from assignments
          const assignedTeachers = generateTeachersFromAssignments(assignments);
          setTeachers(assignedTeachers);
        } else {
          console.error("Failed to fetch teacher assignments:", results[2].reason);
          setTeacherAssignments([]);
          setTeachers([]);
        }

        // Show warning if any critical data failed to load
        const anyDataFailed = results.some(result => result.status === 'rejected');
        if (anyDataFailed) {
          showError("Warning", "Some form data could not be loaded. Some features may be limited.");
        }
      } catch (error) {
        console.error("Error fetching additional data:", error);
        showError("Error", "Failed to load form data");
      }
    };

    fetchAdditionalData();
  }, [schoolId]); // Removed showError from dependencies

  // CRUD Functions
  const handleCreateSchedule = () => {
    setScheduleToEdit({
      class_id: selectedClass !== 'all_classes' ? selectedClass : "",
      subject_id: "",
      teacher_id: "",
      day_of_week: "Monday",
      period_id: "",
      schedule_type: isExamMode ? "Exam" : "Normal"
    });
    setIsTimetableModalOpen(true);
  };

  const handleCellClick = (day: string, periodNumber: number) => {
    // Check if there's already a schedule entry for this slot
    const existingEntry = timetableData[day]?.[periodNumber];
    console.log("existingEntry", existingEntry);
    if (existingEntry) {
      const period = periods.find(p => p.period_number === periodNumber);
      console.log("period", period);

      // Find IDs from names
      const classObj = classes.find(c => c.name === existingEntry.class_name);
      const subjectObj = subjects.find(s => s.name === existingEntry.subject_name);
      const teacherObj = teachers.find(t => `${t.first_name} ${t.last_name}` === existingEntry.teacher_name || t.name === existingEntry.teacher_name);

      // Edit existing entry
      setScheduleToEdit({
        _id: existingEntry._id,
        class_id: classObj?._id || selectedClass,
        subject_id: subjectObj?._id || "",
        teacher_id: teacherObj?._id || "",
        period_id: period?._id || "",
        day_of_week: day,
        schedule_type: existingEntry.schedule_type || "Normal"
      });
    } else {
      // Create new entry with pre-filled day and period
      const period = periods.find(p => p.period_number === periodNumber);
      setScheduleToEdit({
        class_id: selectedClass !== 'all_classes' ? selectedClass : "",
        subject_id: "",
        teacher_id: "",
        day_of_week: day,
        period_id: period?._id || "",
        schedule_type: isExamMode ? "Exam" : "Normal"
      });
    }
    setIsTimetableModalOpen(true);
  };

  // Modal submission function
  const handleScheduleSubmit = async (data: any) => {
    if (!schoolId) {
      showError("Error", "No school ID found");
      return;
    }

    // Check for period conflicts before submitting
    const existingEntry = timetableData[data.day_of_week]?.[periods.find(p => p._id === data.period_id)?.period_number || 0];

    if (existingEntry && (!scheduleToEdit || scheduleToEdit._id !== existingEntry._id)) {
      // Period conflict detected - show replacement option
      const period = periods.find(p => p._id === data.period_id);
      setConflictDetails({
        teacher_name: existingEntry.teacher_name || 'No teacher assigned',
        class_name: existingEntry.class_name,
        subject_name: existingEntry.subject_name,
        period_info: period ? {
          period_number: period.period_number,
          start_time: period.start_time,
          end_time: period.end_time
        } : null,
        day_of_week: data.day_of_week
      });
      setConflictMessage(`This time slot is already occupied by ${existingEntry.subject_name} for ${existingEntry.class_name}. Would you like to replace it?`);
      setConflictType('period');
      setCanReplaceConflict(true);
      setPendingScheduleData(data);
      setIsConflictModalOpen(true);
      setIsTimetableModalOpen(false);
      return;
    }

    await submitScheduleData(data);
  };

  // Function to actually submit the schedule data
  const submitScheduleData = async (data: any) => {
    setIsSubmitting(true);
    try {
      if (scheduleToEdit && scheduleToEdit._id) {
        // Update existing schedule entry
        await updateScheduleEntry(schoolId as string, scheduleToEdit._id, data);
        showSuccess("Schedule Updated", "Schedule entry has been updated successfully.");
      } else {
        // Create new schedule entry
        await createScheduleEntry(schoolId as string, data);
        showSuccess("Schedule Created", "Schedule entry has been created successfully.");
      }

      // Refresh timetable
      await refreshTimetableData();
      setIsTimetableModalOpen(false);
      setScheduleToEdit(null);
    } catch (error) {
      console.error("Error submitting schedule:", error);

      // Check if it's a teacher conflict error
      if (error instanceof Error && 'conflict' in error && (error as TeacherConflictError).conflict) {
        const conflictError = error as TeacherConflictError;
        setConflictDetails(conflictError.conflictDetails);
        setConflictMessage(conflictError.message);
        setConflictType('teacher');
        setCanReplaceConflict(false);
        setIsConflictModalOpen(true);
        setIsTimetableModalOpen(false);
      } else {
        showError("Error", "Failed to save schedule. Please try again.");
      }
    } finally {
      setIsSubmitting(false);
    }
  };

  // Function to refresh timetable data
  const refreshTimetableData = async () => {
    const filters: any = {};
    if (selectedClass !== 'all_classes') filters.class_id = selectedClass;

    if (selectedClass === 'all_classes') {
      // Refresh all classes data
      const allClassesResponse = await getOrganizedTimetable(schoolId as string, {});
      const scheduleResponse = await getTimetable(schoolId as string, {});
      const scheduleEntries = scheduleResponse.schedule_records;

      const classGroupedTimetables: {[className: string]: TimetableData} = {};
      scheduleEntries.forEach((entry: ScheduleEntry) => {
        const isExamEntry = entry.schedule_type === 'Exam';
        if (isExamMode && !isExamEntry) return;
        if (!isExamMode && isExamEntry) return;

        if (!classGroupedTimetables[entry.class_name]) {
          classGroupedTimetables[entry.class_name] = {};
          DAYS.forEach(day => {
            classGroupedTimetables[entry.class_name][day] = {};
          });
        }

        if (!classGroupedTimetables[entry.class_name][entry.day_of_week]) {
          classGroupedTimetables[entry.class_name][entry.day_of_week] = {};
        }

        classGroupedTimetables[entry.class_name][entry.day_of_week][entry.period_number] = entry;
      });

      setAllClassesTimetable(classGroupedTimetables);
      const filteredMainTimetable = filterTimetableData(allClassesResponse.timetable);
      setTimetableData(filteredMainTimetable);
      setPeriods(allClassesResponse.periods);
    } else {
      // Refresh specific class data
      const response = await getOrganizedTimetable(schoolId as string, filters);
      const filteredTimetable = filterTimetableData(response.timetable);
      setTimetableData(filteredTimetable);
      setPeriods(response.periods);
    }
  };

  // Handle conflict replacement
  const handleReplaceConflict = async () => {
    if (!pendingScheduleData) return;

    // First delete the existing entry, then create the new one
    const existingEntry = timetableData[pendingScheduleData.day_of_week]?.[periods.find(p => p._id === pendingScheduleData.period_id)?.period_number || 0];

    if (existingEntry) {
      try {
        await deleteScheduleEntry(schoolId as string, existingEntry._id);
      } catch (error) {
        console.error("Error deleting existing entry:", error);
        showError("Error", "Failed to replace existing entry");
        return;
      }
    }

    // Now submit the new schedule data
    await submitScheduleData(pendingScheduleData);

    // Clean up conflict modal state
    setIsConflictModalOpen(false);
    setConflictDetails(null);
    setConflictMessage("");
    setPendingScheduleData(null);
    setCanReplaceConflict(false);
  };

  // Handle schedule deletion
  const handleDeleteSchedule = (scheduleEntry: ScheduleEntry) => {
    setScheduleToDelete(scheduleEntry);
    setIsDeleteModalOpen(true);
  };

  // Confirm schedule deletion
  const confirmDeleteSchedule = async () => {
    if (!scheduleToDelete || !schoolId) {
      showError("Error", "Unable to delete schedule entry");
      return;
    }

    setIsSubmitting(true);
    try {
      await deleteScheduleEntry(schoolId as string, scheduleToDelete._id);
      showSuccess("Schedule Deleted", "Schedule entry has been deleted successfully.");

      // Refresh timetable data
      const filters: any = {};
      if (selectedClass !== 'all_classes') filters.class_id = selectedClass;

      if (selectedClass === 'all_classes') {
        // Refresh all classes data
        const allClassesResponse = await getOrganizedTimetable(schoolId as string, {});
        const scheduleResponse = await getTimetable(schoolId as string, {});
        const scheduleEntries = scheduleResponse.schedule_records;

        const classGroupedTimetables: {[className: string]: TimetableData} = {};
        scheduleEntries.forEach((entry: ScheduleEntry) => {
          const isExamEntry = entry.schedule_type === 'Exam';
          if (isExamMode && !isExamEntry) return;
          if (!isExamMode && isExamEntry) return;

          if (!classGroupedTimetables[entry.class_name]) {
            classGroupedTimetables[entry.class_name] = {};
            DAYS.forEach(day => {
              classGroupedTimetables[entry.class_name][day] = {};
            });
          }

          if (!classGroupedTimetables[entry.class_name][entry.day_of_week]) {
            classGroupedTimetables[entry.class_name][entry.day_of_week] = {};
          }

          classGroupedTimetables[entry.class_name][entry.day_of_week][entry.period_number] = entry;
        });

        setAllClassesTimetable(classGroupedTimetables);
        const filteredMainTimetable = filterTimetableData(allClassesResponse.timetable);
        setTimetableData(filteredMainTimetable);
        setPeriods(allClassesResponse.periods);
      } else {
        // Refresh specific class data
        const response = await getOrganizedTimetable(schoolId as string, filters);
        const filteredTimetable = filterTimetableData(response.timetable);
        setTimetableData(filteredTimetable);
        setPeriods(response.periods);
      }

      setIsDeleteModalOpen(false);
      setScheduleToDelete(null);
    } catch (error) {
      console.error("Error deleting schedule:", error);
      showError("Error", "Failed to delete schedule. Please try again.");
    } finally {
      setIsSubmitting(false);
    }
  };

  const getSubjectColor = (subject: string) => {
    const colors = {
      Mathematics: 'bg-blue-100 text-blue-800 border-blue-200 dark:bg-blue-900/30 dark:text-blue-300',
      Physics: 'bg-purple-100 text-purple-800 border-purple-200 dark:bg-purple-900/30 dark:text-purple-300',
      Chemistry: 'bg-green-100 text-green-800 border-green-200 dark:bg-green-900/30 dark:text-green-300',
      Biology: 'bg-orange-100 text-orange-800 border-orange-200 dark:bg-orange-900/30 dark:text-orange-300',
      English: 'bg-red-100 text-red-800 border-red-200 dark:bg-red-900/30 dark:text-red-300',
      History: 'bg-yellow-100 text-yellow-800 border-yellow-200 dark:bg-yellow-900/30 dark:text-yellow-300'
    };
    return colors[subject as keyof typeof colors] || 'bg-gray-100 text-gray-800 border-gray-200 dark:bg-gray-900/30 dark:text-gray-300';
  };



  if (loadingData) {
    return (
      <ProtectedRoute allowedRoles={["school_admin", "admin", "super"]}>
        <SchoolLayout navigation={navigation} onLogout={logout}>
          <SchoolAdminTimetableSkeleton />
        </SchoolLayout>
      </ProtectedRoute>
    );
  }

  return (
    <ProtectedRoute allowedRoles={["school_admin", "admin", "super"]}>
      <SchoolLayout navigation={navigation} onLogout={logout}>
        <div className="space-y-6">
          {/* Header */}
          <div className="bg-widget rounded-lg border border-stroke p-6">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <div className="w-12 h-12 bg-teal rounded-full flex items-center justify-center">
                  <Clock4 className="h-6 w-6 text-white" />
                </div>
                <div>
                  <div className="flex items-center space-x-3">
                    <h1 className="text-2xl font-bold text-foreground">Time Table Management</h1>
                    <span className={`px-3 py-1 rounded-full text-xs font-medium ${
                      isExamMode
                        ? 'bg-orange-100 text-orange-800 dark:bg-orange-900/30 dark:text-orange-300'
                        : 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300'
                    }`}>
                      {isExamMode ? 'EXAM MODE' : 'NORMAL MODE'}
                    </span>
                  </div>
                  <p className="text-foreground/60">
                    {isExamMode
                      ? 'Manage exam schedules and periods'
                      : 'Create and manage class schedules and time tables'
                    }
                  </p>
                </div>
              </div>
            </div>
          </div>

          {/* Filters */}
          <div className="bg-widget rounded-lg border border-stroke p-6">
            <div className="flex flex-wrap items-center gap-4">
              <div className="flex items-center space-x-2">
                <Filter className="h-4 w-4 text-foreground/60" />
                <span className="text-sm font-medium text-foreground">View:</span>
              </div>

              {/* Exam Mode Toggle */}
              <div className="flex items-center space-x-3 px-4 py-2 bg-gray-50 dark:bg-gray-800 rounded-lg border border-stroke">
                <span className="text-sm font-medium text-foreground">Mode:</span>
                <div className="flex items-center space-x-2">
                  <span className={`text-sm ${!isExamMode ? 'text-teal font-medium' : 'text-foreground/60'}`}>
                    Normal
                  </span>
                  <button
                    onClick={() => setIsExamMode(!isExamMode)}
                    className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors focus:outline-none focus:ring-2 focus:ring-teal focus:ring-offset-2 ${
                      isExamMode ? 'bg-teal' : 'bg-gray-300 dark:bg-gray-600'
                    }`}
                  >
                    <span
                      className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                        isExamMode ? 'translate-x-6' : 'translate-x-1'
                      }`}
                    />
                  </button>
                  <span className={`text-sm ${isExamMode ? 'text-teal font-medium' : 'text-foreground/60'}`}>
                    Exam
                  </span>
                </div>
              </div>
              
              <div className="flex items-center space-x-2">
                <label className="text-sm text-foreground/70">Class:</label>
                <select
                  value={selectedClass}
                  onChange={(e) => setSelectedClass(e.target.value)}
                  className="px-3 py-2 border border-stroke rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-teal bg-widget text-foreground"
                >
                  <option value="all_classes">All Classes</option>
                  {classes.map((classItem) => (
                    <option key={classItem._id} value={classItem._id}>
                      {classItem.name}
                    </option>
                  ))}
                </select>
              </div>


            </div>
          </div>

          {/* Teacher Assignment Legend */}
          <div className="bg-widget rounded-lg border border-stroke p-4">
            <h3 className="text-sm font-medium text-foreground mb-3">Teacher Assignment Indicators:</h3>
            <div className="flex flex-wrap gap-4 text-xs">
              <div className="flex items-center space-x-2">
                <div className="w-3 h-3 bg-red-500 rounded-full border border-white"></div>
                <span className="text-foreground/70">Teacher mismatch with assignments</span>
              </div>
              <div className="flex items-center space-x-2">
                <div className="w-3 h-3 bg-yellow-500 rounded-full border border-white"></div>
                <span className="text-foreground/70">Missing teacher assignment</span>
              </div>
              <div className="flex items-center space-x-2">
                <div className="w-3 h-3 bg-green-500 rounded-full border border-white"></div>
                <span className="text-foreground/70">Teacher assignment matches</span>
              </div>
            </div>
          </div>

          {/* Timetable Grid */}
          <div className="bg-widget rounded-lg border border-stroke p-6">
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-lg font-semibold text-foreground">
                {isExamMode ? 'Exam Schedule' : 'Weekly Schedule'} - {selectedClass === 'all_classes' ? 'All Classes' : classes.find(c => c._id === selectedClass)?.name || 'Unknown Class'}
              </h2>

              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                onClick={handleCreateSchedule}
                className="flex items-center space-x-2 px-4 py-2 bg-teal text-white rounded-md hover:bg-teal-600"
              >
                <Plus size={16} />
                <span>{isExamMode ? 'Add Exam' : 'Add Schedule'}</span>
              </motion.button>
            </div>

            {/* Empty State Message for Exam Mode */}
            {isExamMode && Object.keys(allClassesTimetable).length === 0 && selectedClass === 'all_classes' && (
              <div className="text-center py-12">
                <div className="w-16 h-16 bg-orange-100 dark:bg-orange-900/30 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Calendar className="h-8 w-8 text-orange-600 dark:text-orange-400" />
                </div>
                <h3 className="text-lg font-medium text-foreground mb-2">No Exams Scheduled</h3>
                <p className="text-foreground/60 mb-4">
                  There are no exam entries in the timetable yet. Click "Add Exam" to schedule exams.
                </p>
              </div>
            )}

            {isExamMode && Object.keys(timetableData).every(day => Object.keys(timetableData[day]).length === 0) && selectedClass !== 'all_classes' && (
              <div className="text-center py-12">
                <div className="w-16 h-16 bg-orange-100 dark:bg-orange-900/30 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Calendar className="h-8 w-8 text-orange-600 dark:text-orange-400" />
                </div>
                <h3 className="text-lg font-medium text-foreground mb-2">No Exams Scheduled for This Class</h3>
                <p className="text-foreground/60 mb-4">
                  There are no exam entries for this class yet. Click "Add Exam" to schedule exams.
                </p>
              </div>
            )}

            {selectedClass === 'all_classes' ? (
              // All Classes View
              <div className="space-y-8">
                {Object.entries(allClassesTimetable).map(([className, classTimetable]) => (
                  <div key={className} className="border border-stroke rounded-lg p-4">
                    <h3 className="text-lg font-semibold text-foreground mb-4 bg-gray-50 dark:bg-gray-800 p-3 rounded-md">
                      {className}
                    </h3>
                    <div className="overflow-x-auto">
                      <table className="w-full border-collapse">
                        <thead>
                          <tr>
                            <th className="border border-stroke p-2 bg-gray-50 dark:bg-gray-800 text-left font-semibold text-foreground text-sm">
                              Period
                            </th>
                            {DAYS.map(day => (
                              <th key={day} className="border border-stroke p-2 bg-gray-50 dark:bg-gray-800 text-center font-semibold text-foreground min-w-[140px] text-sm">
                                {day}
                              </th>
                            ))}
                          </tr>
                        </thead>
                        <tbody>
                          {periods.map(period => (
                            <tr key={period.period_number}>
                              <td className="border border-stroke p-2 bg-gray-50 dark:bg-gray-800 font-medium text-foreground text-sm">
                                <div className="text-center">
                                  <div className="font-semibold">P{period.period_number}</div>
                                  <div className="text-xs text-foreground/60">
                                    {period.start_time.slice(0, 5)}-{period.end_time.slice(0, 5)}
                                  </div>
                                </div>
                              </td>
                              {DAYS.map(day => {
                                const scheduleEntry = classTimetable[day]?.[period.period_number];
                                return (
                                  <td
                                    key={`${className}-${day}-${period.period_number}`}
                                    className="border border-stroke p-1"
                                  >
                                    {scheduleEntry ? (
                                      <div className={`p-2 rounded border ${getSubjectColor(scheduleEntry.subject_name)} text-xs`}>
                                        <div className="font-semibold mb-1">
                                          {scheduleEntry.subject_name}
                                        </div>
                                        <div className="opacity-80">
                                          {scheduleEntry.teacher_name}
                                        </div>
                                      </div>
                                    ) : (
                                      <div className="p-2 text-center text-foreground/30 text-xs">
                                        Free
                                      </div>
                                    )}
                                  </td>
                                );
                              })}
                            </tr>
                          ))}
                        </tbody>
                      </table>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              // Single Class View
              <div className="overflow-x-auto">
                <table className="w-full border-collapse">
                  <thead>
                    <tr>
                      <th className="border border-stroke p-3 bg-gray-50 dark:bg-gray-800 text-left font-semibold text-foreground">
                        Period / Day
                      </th>
                      {DAYS.map(day => (
                        <th key={day} className="border border-stroke p-3 bg-gray-50 dark:bg-gray-800 text-center font-semibold text-foreground min-w-[180px]">
                          {day}
                        </th>
                      ))}
                    </tr>
                  </thead>
                  <tbody>
                    {periods.map(period => (
                      <tr key={period.period_number}>
                        <td className="border border-stroke p-3 bg-gray-50 dark:bg-gray-800 font-medium text-foreground">
                          <div className="text-center">
                            <div className="font-semibold">P {period.period_number}</div>
                            <div className="text-xs text-foreground/60">
                              {period.start_time.slice(0, 5)} - {period.end_time.slice(0, 5)}
                            </div>
                          </div>
                        </td>
                        {DAYS.map(day => {
                          const scheduleEntry = timetableData[day]?.[period.period_number];
                          return (
                            <td
                              key={`${day}-${period.period_number}`}
                              className="border border-stroke p-2 cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors"
                              onClick={() => handleCellClick(day, period.period_number)}
                            >
                              {scheduleEntry ? (
                                (() => {
                                  const overlapInfo = checkTeacherAssignmentOverlap(scheduleEntry);
                                  return (
                                    <div className={`p-3 rounded-lg border-2 ${getSubjectColor(scheduleEntry.subject_name)} relative group`}>
                                      <div className="font-semibold text-sm mb-1">
                                        {scheduleEntry.subject_name}
                                      </div>
                                      <div className="text-xs opacity-80 mb-1">
                                        {scheduleEntry.teacher_name || 'No teacher assigned'}
                                      </div>
                                      <div className="text-xs opacity-70">
                                        {scheduleEntry.class_name}
                                      </div>

                                      {/* Teacher Assignment Overlap Indicator */}
                                      {overlapInfo.hasOverlap && (
                                        <div className="absolute top-1 left-1">
                                          {overlapInfo.conflictType === 'no_assignment' ? (
                                            <div
                                              className="w-3 h-3 bg-yellow-500 rounded-full border border-white"
                                              title={`Teacher assignment exists: ${overlapInfo.assignedTeacher} should be assigned to this class`}
                                            />
                                          ) : overlapInfo.conflictType === 'different_teacher' ? (
                                            <div
                                              className="w-3 h-3 bg-red-500 rounded-full border border-white"
                                              title={`Teacher mismatch: ${overlapInfo.assignedTeacher} is assigned to this class, but ${scheduleEntry.teacher_name} is scheduled`}
                                            />
                                          ) : null}
                                        </div>
                                      )}

                                      {/* Action buttons on hover */}
                                      <div className="absolute top-1 right-1 opacity-0 group-hover:opacity-100 transition-opacity flex space-x-1">
                                        <button
                                          onClick={(e) => {
                                            e.stopPropagation();
                                            handleDeleteSchedule(scheduleEntry);
                                          }}
                                          className="p-1 bg-red-500 hover:bg-red-600 text-white rounded shadow-sm"
                                          title="Delete schedule"
                                        >
                                          <Trash2 className="h-3 w-3" />
                                        </button>
                                        <button
                                          className="p-1 bg-white dark:bg-gray-800 rounded shadow-sm"
                                          title="Edit schedule"
                                        >
                                          <Edit className="h-3 w-3 text-foreground/60" />
                                        </button>
                                      </div>
                                    </div>
                                  );
                                })()
                              ) : (
                                <div className="p-3 rounded-lg border-2 border-dashed border-gray-300 dark:border-gray-600 text-center text-foreground/40 hover:border-teal hover:text-teal transition-colors">
                                  <Plus className="h-4 w-4 mx-auto mb-1" />
                                  <div className="text-xs">{isExamMode ? 'Add Exam' : 'Add Class'}</div>
                                </div>
                              )}
                            </td>
                          );
                        })}
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            )}
          </div>

          {/* Quick Stats */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            <div className="bg-widget rounded-lg border border-stroke p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-foreground/60">
                    {selectedClass === 'all_classes'
                      ? (isExamMode ? 'Total Exams' : 'Total Classes')
                      : (isExamMode ? 'Scheduled Exams' : 'Scheduled Classes')
                    }
                  </p>
                  <p className="text-2xl font-bold text-foreground">
                    {selectedClass === 'all_classes'
                      ? Object.values(allClassesTimetable).reduce((total, classTimetable) =>
                          total + Object.values(classTimetable).reduce((classTotal, day) =>
                            classTotal + Object.values(day).filter(entry => entry !== null).length, 0
                          ), 0
                        )
                      : Object.values(timetableData).reduce((total, day) =>
                          total + Object.values(day).filter(entry => entry !== null).length, 0
                        )
                    }
                  </p>
                </div>
                <div className="w-10 h-10 bg-blue-500 rounded-lg flex items-center justify-center">
                  <BookOpen className="h-5 w-5 text-white" />
                </div>
              </div>
            </div>

            <div className="bg-widget rounded-lg border border-stroke p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-foreground/60">
                    {selectedClass === 'all_classes'
                      ? (isExamMode ? 'All Free Exam Periods' : 'All Free Periods')
                      : `${isExamMode ? 'Free Exam Periods' : 'Free Periods'} for ${classes.find(c => c._id === selectedClass)?.name || 'Selected Class'}`
                    }
                  </p>
                  <p className="text-2xl font-bold text-foreground">
                    {selectedClass === 'all_classes'
                      ? Object.keys(allClassesTimetable).length * DAYS.length * periods.length -
                        Object.values(allClassesTimetable).reduce((total, classTimetable) =>
                          total + Object.values(classTimetable).reduce((classTotal, day) =>
                            classTotal + Object.values(day).filter(entry => entry !== null).length, 0
                          ), 0
                        )
                      : DAYS.length * periods.length - Object.values(timetableData).reduce((total, day) =>
                          total + Object.values(day).filter(entry => entry !== null).length, 0
                        )
                    }
                  </p>
                </div>
                <div className="w-10 h-10 bg-green-500 rounded-lg flex items-center justify-center">
                  <Calendar className="h-5 w-5 text-white" />
                </div>
              </div>
            </div>

            <div className="bg-widget rounded-lg border border-stroke p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-foreground/60">
                    {selectedClass === 'all_classes'
                      ? (isExamMode ? 'All Exam Supervisors' : 'All Teachers')
                      : (isExamMode ? 'Exam Supervisors for Class' : 'Teachers for Class')
                    }
                  </p>
                  <p className="text-2xl font-bold text-foreground">
                    {selectedClass === 'all_classes'
                      ? new Set(
                          Object.values(allClassesTimetable)
                            .flatMap(classTimetable =>
                              Object.values(classTimetable)
                                .flatMap(day => Object.values(day))
                                .filter(entry => entry !== null)
                                .map(entry => entry!.teacher_name)
                            )
                        ).size
                      : new Set(
                          Object.values(timetableData)
                            .flatMap(day => Object.values(day))
                            .filter(entry => entry !== null)
                            .map(entry => entry!.teacher_name)
                        ).size
                    }
                  </p>
                </div>
                <div className="w-10 h-10 bg-purple-500 rounded-lg flex items-center justify-center">
                  <Users className="h-5 w-5 text-white" />
                </div>
              </div>
            </div>

            <div className="bg-widget rounded-lg border border-stroke p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-foreground/60">
                    {selectedClass === 'all_classes'
                      ? (isExamMode ? 'All Exam Subjects' : 'All Subjects')
                      : (isExamMode ? 'Exam Subjects for Class' : 'Subjects for Class')
                    }
                  </p>
                  <p className="text-2xl font-bold text-foreground">
                    {selectedClass === 'all_classes'
                      ? new Set(
                          Object.values(allClassesTimetable)
                            .flatMap(classTimetable =>
                              Object.values(classTimetable)
                                .flatMap(day => Object.values(day))
                                .filter(entry => entry !== null)
                                .map(entry => entry!.subject_name)
                            )
                        ).size
                      : new Set(
                          Object.values(timetableData)
                            .flatMap(day => Object.values(day))
                            .filter(entry => entry !== null)
                            .map(entry => entry!.subject_name)
                        ).size
                    }
                  </p>
                </div>
                <div className="w-10 h-10 bg-orange-500 rounded-lg flex items-center justify-center">
                  <BookOpen className="h-5 w-5 text-white" />
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Timetable Modal */}
        <TimetableModal
          isOpen={isTimetableModalOpen}
          onClose={() => {
            setIsTimetableModalOpen(false);
            setScheduleToEdit(null);
          }}
          onSubmit={handleScheduleSubmit}
          schedule={scheduleToEdit}
          classes={classes}
          subjects={subjects}
          teachers={teachers}
          periods={periods}
          loading={isSubmitting}
          preSelectedClass={selectedClass !== 'all_classes' ? selectedClass : undefined}
          isClassLocked={selectedClass !== 'all_classes'}
          isExamMode={isExamMode}
        />

        {/* Schedule Conflict Modal */}
        {conflictDetails && (
          <ScheduleConflictModal
            isOpen={isConflictModalOpen}
            onClose={() => {
              setIsConflictModalOpen(false);
              setConflictDetails(null);
              setConflictMessage("");
              setPendingScheduleData(null);
              setCanReplaceConflict(false);
            }}
            onReplace={canReplaceConflict ? handleReplaceConflict : undefined}
            conflictDetails={conflictDetails}
            message={conflictMessage}
            conflictType={conflictType}
            canReplace={canReplaceConflict}
            isExamMode={isExamMode}
            loading={isSubmitting}
          />
        )}

        {/* Delete Confirmation Modal */}
        <ScheduleDeleteModal
          isOpen={isDeleteModalOpen}
          onClose={() => {
            setIsDeleteModalOpen(false);
            setScheduleToDelete(null);
          }}
          onConfirm={confirmDeleteSchedule}
          scheduleEntry={scheduleToDelete}
          isExamMode={isExamMode}
          loading={isSubmitting}
        />

        {/* Toast Notifications */}
        <ToastContainer toasts={toasts} onClose={removeToast} />
      </SchoolLayout>
    </ProtectedRoute>
  );
}
