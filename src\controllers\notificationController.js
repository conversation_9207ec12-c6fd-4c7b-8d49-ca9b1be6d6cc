const Notification = require('../models/Notification');
const User = require('../models/User');
const School = require('../models/School');
const nodemailer = require('nodemailer');
const { Vonage } = require('@vonage/server-sdk');

// Test route
const testNotificationResponse = (req, res) => {
  res.status(200).json({ message: 'Notification route is working!' });
};

// Get notifications for current user
const getUserNotifications = async (req, res) => {
  try {
    const userId = req.user._id;
    const { page = 1, limit = 20, unread_only = false, category } = req.query;

    const query = { recipient_id: userId };
    
    if (unread_only === 'true') {
      query.read = false;
    }
    
    if (category) {
      query.category = category;
    }

    // Don't show expired notifications
    query.$or = [
      { expires_at: { $exists: false } },
      { expires_at: { $gt: new Date() } }
    ];

    const notifications = await Notification.find(query)
      .populate('sender_id', 'first_name last_name')
      .populate('school_id', 'name')
      .sort({ createdAt: -1 })
      .limit(limit * 1)
      .skip((page - 1) * limit);

    const total = await Notification.countDocuments(query);
    const unreadCount = await Notification.countDocuments({
      recipient_id: userId,
      read: false,
      $or: [
        { expires_at: { $exists: false } },
        { expires_at: { $gt: new Date() } }
      ]
    });

    res.status(200).json({
      notifications,
      pagination: {
        current_page: parseInt(page),
        total_pages: Math.ceil(total / limit),
        total_items: total,
        items_per_page: parseInt(limit)
      },
      unread_count: unreadCount
    });
  } catch (error) {
    console.error('Error fetching user notifications:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
};

// Mark notification as read
const markNotificationAsRead = async (req, res) => {
  try {
    const { notificationId } = req.params;
    const userId = req.user._id;

    const notification = await Notification.findOne({
      _id: notificationId,
      recipient_id: userId
    });

    if (!notification) {
      return res.status(404).json({ message: 'Notification not found' });
    }

    await notification.markAsRead();

    res.status(200).json({ message: 'Notification marked as read' });
  } catch (error) {
    console.error('Error marking notification as read:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
};

// Mark all notifications as read
const markAllNotificationsAsRead = async (req, res) => {
  try {
    const userId = req.user._id;

    await Notification.updateMany(
      { recipient_id: userId, read: false },
      { 
        read: true, 
        read_at: new Date() 
      }
    );

    res.status(200).json({ message: 'All notifications marked as read' });
  } catch (error) {
    console.error('Error marking all notifications as read:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
};

// Delete notification
const deleteNotification = async (req, res) => {
  try {
    const { notificationId } = req.params;
    const userId = req.user._id;

    const notification = await Notification.findOneAndDelete({
      _id: notificationId,
      recipient_id: userId
    });

    if (!notification) {
      return res.status(404).json({ message: 'Notification not found' });
    }

    res.status(200).json({ message: 'Notification deleted successfully' });
  } catch (error) {
    console.error('Error deleting notification:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
};

// Create notification (for admins/system)
const createNotification = async (req, res) => {
  try {
    const {
      recipient_ids,
      recipient_type = 'user',
      school_id,
      type,
      category,
      title,
      message,
      action_url,
      action_label,
      priority = 'normal',
      channels = { in_app: true },
      expires_at,
      scheduled_for
    } = req.body;

    if (!recipient_ids || !Array.isArray(recipient_ids) || recipient_ids.length === 0) {
      return res.status(400).json({ message: 'Recipient IDs are required' });
    }

    if (!type || !category || !title || !message) {
      return res.status(400).json({ message: 'Type, category, title, and message are required' });
    }

    const notificationData = {
      recipient_type,
      school_id,
      type,
      category,
      title,
      message,
      action_url,
      action_label,
      priority,
      channels,
      expires_at,
      scheduled_for,
      sender_id: req.user._id,
      sender_type: 'user'
    };

    let notifications;
    if (recipient_ids.length === 1) {
      // Single notification
      const notification = new Notification({
        ...notificationData,
        recipient_id: recipient_ids[0],
        notification_id: `NOT_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
      });
      notifications = [await notification.save()];
    } else {
      // Bulk notification
      notifications = await Notification.createBulkNotification(recipient_ids, notificationData);
    }

    // Send via other channels if requested
    for (const notification of notifications) {
      await processNotificationDelivery(notification);
    }

    res.status(201).json({
      message: 'Notification(s) created successfully',
      count: notifications.length
    });
  } catch (error) {
    console.error('Error creating notification:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
};

// Get notification statistics
const getNotificationStats = async (req, res) => {
  try {
    const userId = req.user._id;

    const stats = await Notification.aggregate([
      { $match: { recipient_id: userId } },
      {
        $group: {
          _id: null,
          total: { $sum: 1 },
          unread: { $sum: { $cond: [{ $eq: ['$read', false] }, 1, 0] } },
          by_category: {
            $push: {
              category: '$category',
              read: '$read'
            }
          }
        }
      }
    ]);

    const categoryStats = {};
    if (stats.length > 0) {
      stats[0].by_category.forEach(item => {
        if (!categoryStats[item.category]) {
          categoryStats[item.category] = { total: 0, unread: 0 };
        }
        categoryStats[item.category].total++;
        if (!item.read) {
          categoryStats[item.category].unread++;
        }
      });
    }

    res.status(200).json({
      total: stats.length > 0 ? stats[0].total : 0,
      unread: stats.length > 0 ? stats[0].unread : 0,
      by_category: categoryStats
    });
  } catch (error) {
    console.error('Error fetching notification stats:', error);
    res.status(500).json({ message: 'Internal server error', error:error });
  }
};

// Helper function to process notification delivery
const processNotificationDelivery = async (notification) => {
  try {
    // Mark in-app as delivered immediately
    if (notification.channels.in_app) {
      notification.delivery_status.in_app.delivered = true;
      notification.delivery_status.in_app.delivered_at = new Date();
    }

    // Send email if requested
    if (notification.channels.email) {
      await sendEmailNotification(notification);
    }

    // Send SMS if requested
    if (notification.channels.sms) {
      await sendSMSNotification(notification);
    }

    await notification.save();
  } catch (error) {
    console.error('Error processing notification delivery:', error);
  }
};

// Helper function to send email notification
const sendEmailNotification = async (notification) => {
  try {
    const user = await User.findById(notification.recipient_id);
    if (!user || !user.email) return;

    const transporter = nodemailer.createTransporter({
      service: 'gmail',
      auth: {
        user: process.env.EMAIL_USER,
        pass: process.env.EMAIL_PASS,
      },
    });

    const mailOptions = {
      from: process.env.EMAIL_USER,
      to: user.email,
      subject: notification.title,
      html: `
        <h2>${notification.title}</h2>
        <p>${notification.message}</p>
        ${notification.action_url ? `<a href="${notification.action_url}" style="background-color: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">${notification.action_label || 'View Details'}</a>` : ''}
      `
    };

    await transporter.sendMail(mailOptions);
    
    notification.delivery_status.email.delivered = true;
    notification.delivery_status.email.delivered_at = new Date();
  } catch (error) {
    console.error('Error sending email notification:', error);
    notification.delivery_status.email.error = error.message;
  }
};

// Helper function to send SMS notification
const sendSMSNotification = async (notification) => {
  try {
    const user = await User.findById(notification.recipient_id);
    if (!user || !user.phone) return;

    const vonage = new Vonage({
      apiKey: process.env.VONAGE_API_KEY,
      apiSecret: process.env.VONAGE_API_SECRET
    });

    const message = `${notification.title}: ${notification.message}`;
    
    await vonage.sms.send({
      to: user.phone,
      from: 'Scholarify',
      text: message.substring(0, 160) // SMS character limit
    });

    notification.delivery_status.sms.delivered = true;
    notification.delivery_status.sms.delivered_at = new Date();
  } catch (error) {
    console.error('Error sending SMS notification:', error);
    notification.delivery_status.sms.error = error.message;
  }
};

module.exports = {
  testNotificationResponse,
  getUserNotifications,
  markNotificationAsRead,
  markAllNotificationsAsRead,
  deleteNotification,
  createNotification,
  getNotificationStats
};
