const mongoose = require('mongoose');

const notificationSchema = new mongoose.Schema({
  notification_id: {
    type: String,
    required: true,
    unique: true
  },
  
  // Recipient information
  recipient_id: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  recipient_type: {
    type: String,
    enum: ['user', 'school', 'class', 'all'],
    default: 'user'
  },
  
  // School context (for school-specific notifications)
  school_id: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'School',
    required: false
  },
  
  // Notification content
  type: {
    type: String,
    enum: ['info', 'success', 'warning', 'error', 'announcement', 'reminder', 'system'],
    required: true
  },
  category: {
    type: String,
    enum: [
      'academic', 'financial', 'attendance', 'grades', 'announcement', 
      'system', 'staff', 'student', 'parent', 'payment', 'schedule'
    ],
    required: true
  },
  title: {
    type: String,
    required: true,
    maxlength: 200
  },
  message: {
    type: String,
    required: true,
    maxlength: 1000
  },
  
  // Action information (optional)
  action_url: {
    type: String,
    required: false
  },
  action_label: {
    type: String,
    required: false,
    maxlength: 50
  },
  
  // Status and metadata
  read: {
    type: Boolean,
    default: false
  },
  read_at: {
    type: Date,
    required: false
  },
  priority: {
    type: String,
    enum: ['low', 'normal', 'high', 'urgent'],
    default: 'normal'
  },
  
  // Delivery channels
  channels: {
    in_app: {
      type: Boolean,
      default: true
    },
    email: {
      type: Boolean,
      default: false
    },
    sms: {
      type: Boolean,
      default: false
    }
  },
  
  // Delivery status
  delivery_status: {
    in_app: {
      delivered: { type: Boolean, default: false },
      delivered_at: { type: Date }
    },
    email: {
      delivered: { type: Boolean, default: false },
      delivered_at: { type: Date },
      error: { type: String }
    },
    sms: {
      delivered: { type: Boolean, default: false },
      delivered_at: { type: Date },
      error: { type: String }
    }
  },
  
  // Sender information
  sender_id: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: false // System notifications won't have a sender
  },
  sender_type: {
    type: String,
    enum: ['user', 'system', 'automated'],
    default: 'system'
  },
  
  // Related entities (for context)
  related_entity: {
    entity_type: {
      type: String,
      enum: ['student', 'class', 'grade', 'attendance', 'payment', 'announcement', 'staff'],
      required: false
    },
    entity_id: {
      type: mongoose.Schema.Types.ObjectId,
      required: false
    }
  },
  
  // Scheduling
  scheduled_for: {
    type: Date,
    required: false // For scheduled notifications
  },
  sent_at: {
    type: Date,
    default: Date.now
  },
  
  // Expiration
  expires_at: {
    type: Date,
    required: false
  },
  
  // Bulk notification reference
  bulk_notification_id: {
    type: String,
    required: false // For grouping bulk notifications
  },
  
  // Metadata
  metadata: {
    type: mongoose.Schema.Types.Mixed,
    default: {}
  }
}, {
  timestamps: true
});

// Indexes for performance
notificationSchema.index({ recipient_id: 1, read: 1, createdAt: -1 });
notificationSchema.index({ school_id: 1, createdAt: -1 });
notificationSchema.index({ category: 1, type: 1 });
notificationSchema.index({ scheduled_for: 1 });
notificationSchema.index({ expires_at: 1 });
notificationSchema.index({ bulk_notification_id: 1 });

// Virtual for checking if notification is expired
notificationSchema.virtual('isExpired').get(function() {
  return this.expires_at && this.expires_at < new Date();
});

// Static method to create system notification
notificationSchema.statics.createSystemNotification = function(data) {
  return this.create({
    ...data,
    sender_type: 'system',
    notification_id: `SYS_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  });
};

// Static method to create bulk notifications
notificationSchema.statics.createBulkNotification = async function(recipients, notificationData) {
  const bulkId = `BULK_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  
  const notifications = recipients.map(recipientId => ({
    ...notificationData,
    recipient_id: recipientId,
    bulk_notification_id: bulkId,
    notification_id: `${bulkId}_${recipientId}`
  }));
  
  return this.insertMany(notifications);
};

// Instance method to mark as read
notificationSchema.methods.markAsRead = function() {
  this.read = true;
  this.read_at = new Date();
  return this.save();
};

// Instance method to check if notification should be delivered via channel
notificationSchema.methods.shouldDeliverVia = function(channel) {
  return this.channels[channel] && !this.delivery_status[channel].delivered;
};

const Notification = mongoose.models.Notification || mongoose.model('Notification', notificationSchema);

module.exports = Notification;
