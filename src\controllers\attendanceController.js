const mongoose = require('mongoose');
const Attendance = require('../models/Attendance');
const Student = require('../models/Student');
const ClassSchedule = require('../models/ClassSchedule');
const Class = require('../models/Class');
const Subject = require('../models/Subject');
const User = require('../models/User');
const Period = require('../models/Periods');
const ActivityLog = require('../models/ActivityLog');

// Get attendance records for a school with filters
const getAttendanceRecords = async (req, res) => {
  try {
    const { school_id } = req.params;
    const {
      date,
      class_id,
      status,
      student_id,
      start_date,
      end_date,
      page = 1,
      limit = 50
    } = req.query;

    if (!school_id) {
      return res.status(400).json({ message: 'School ID is required' });
    }

    // Build filter object
    const filter = { school_id };

    if (date) {
      const targetDate = new Date(date);
      const nextDay = new Date(targetDate);
      nextDay.setDate(nextDay.getDate() + 1);
      filter.date = {
        $gte: targetDate,
        $lt: nextDay
      };
    } else if (start_date && end_date) {
      filter.date = {
        $gte: new Date(start_date),
        $lte: new Date(end_date)
      };
    }

    if (status) filter.status = status;
    if (student_id) filter.student_id = student_id;

    // Calculate pagination
    const skip = (parseInt(page) - 1) * parseInt(limit);

    // Get attendance records with populated data
    const attendanceRecords = await Attendance.find(filter)
      .populate({
        path: 'student_id',
        select: 'first_name last_name student_id _id'
      })
      .populate({
        path: 'school_id',
        select: 'name _id'
      })
      .populate({
        path: 'schedule_id',
        populate: [
          {
            path: 'class_id',
            select: 'name grade_level'
          },
          {
            path: 'subject_id',
            select: 'name'
          },
          {
            path: 'teacher_id',
            select: 'first_name last_name name'
          },
          {
            path: 'period_id',
            select: 'period_number start_time end_time'
          },
        ]
      })
      .sort({ date: -1, createdAt: -1 })
      .skip(skip)
      .limit(parseInt(limit))
      .lean();

    // Get total count for pagination
    const totalRecords = await Attendance.countDocuments(filter);

    // Format the response
    const formattedRecords = attendanceRecords.map(record => ({
      _id: record._id,
      student_name: record.student_id ?
        `${record.student_id.first_name} ${record.student_id.last_name}` : 'Unknown Student',
      student_id: record.student_id?._id || record.student_id?.student_id || 'N/A',
      school_id: record.school_id?._id || 'N/A',
      class_name: record.schedule_id?.class_id?.name || 'Unknown Class',
      subject_name: record.schedule_id?.subject_id?.name || 'Unknown Subject',
      teacher_name: record.schedule_id?.teacher_id ?
          (record.schedule_id.teacher_id.first_name && record.schedule_id.teacher_id.last_name) ?
              `${record.schedule_id.teacher_id.first_name} ${record.schedule_id.teacher_id.last_name}`:
              record.schedule_id.teacher_id.name 
          : 'Unknown Teacher',
      period_number: record.schedule_id?.period_id?.period_number || 0,
      status: record.status,
      date: record.date,
      academic_year: record.academic_year
    }));

    res.status(200).json({
      attendance_records: formattedRecords,
      pagination: {
        current_page: parseInt(page),
        total_pages: Math.ceil(totalRecords / parseInt(limit)),
        total_records: totalRecords,
        per_page: parseInt(limit)
      },
      message: 'Attendance records retrieved successfully'
    });
  } catch (error) {
    console.error('Error fetching attendance records:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
};

// Get attendance statistics for a school
const getAttendanceStats = async (req, res) => {
  try {
    const { school_id } = req.params;
    const { date } = req.query;

    if (!school_id) {
      return res.status(400).json({ message: 'School ID is required' });
    }

    // Use today's date if not provided
    const targetDate = date ? new Date(date) : new Date();
    const nextDay = new Date(targetDate);
    nextDay.setDate(nextDay.getDate() + 1);

    // Get total students in the school
    const totalStudents = await Student.countDocuments({ school_id });

    // Get attendance stats for the target date
    const attendanceStats = await Attendance.aggregate([
      {
        $match: {
          school_id: new mongoose.Types.ObjectId(school_id),
          date: {
            $gte: targetDate,
            $lt: nextDay
          }
        }
      },
      {
        $group: {
          _id: '$status',
          count: { $sum: 1 }
        }
      }
    ]);

    // Format stats
    const stats = {
      totalStudents,
      presentToday: 0,
      absentToday: 0,
      lateToday: 0,
      excusedToday: 0,
      attendanceRate: 0
    };

    attendanceStats.forEach(stat => {
      switch (stat._id) {
        case 'Present':
          stats.presentToday = stat.count;
          break;
        case 'Absent':
          stats.absentToday = stat.count;
          break;
        case 'Late':
          stats.lateToday = stat.count;
          break;
        case 'Excused':
          stats.excusedToday = stat.count;
          break;
      }
    });

    // Calculate attendance rate
    const totalMarked = stats.presentToday + stats.absentToday + stats.lateToday + stats.excusedToday;
    if (totalMarked > 0) {
      stats.attendanceRate = Math.round(((stats.presentToday + stats.lateToday) / totalMarked) * 100);
    }

    res.status(200).json({
      stats,
      date: targetDate.toISOString().split('T')[0],
      message: 'Attendance statistics retrieved successfully'
    });
  } catch (error) {
    console.error('Error fetching attendance stats:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
};

// Export attendance to PDF
const exportAttendancePDF = async (req, res) => {
  try {
    const { school_id } = req.params;
    const { class_id, date, status } = req.query;

    if (!school_id) {
      return res.status(400).json({ message: 'School ID is required' });
    }

    // Convert school_id to ObjectId for proper matching
    const mongoose = require('mongoose');
    const schoolObjectId = new mongoose.Types.ObjectId(school_id);

    // Build filter object
    const filter = { school_id: schoolObjectId };
    if (class_id) filter.class_id = new mongoose.Types.ObjectId(class_id);
    if (date) filter.date = new Date(date);
    if (status) filter.status = status;

    // Get attendance records with populated data
    const attendanceRecords = await Attendance.find(filter)
      .populate({
        path: 'student_id',
        select: 'first_name last_name student_id class_id',
        populate: {
          path: 'class_id',
          select: 'name grade_level'
        }
      })
      .populate('class_schedule_id', 'period_id day_of_week')
      .sort({ date: -1, createdAt: -1 })
      .lean();

    // Format the response
    const formattedRecords = attendanceRecords.map(record => ({
      student_name: record.student_id ?
        `${record.student_id.first_name} ${record.student_id.last_name}` : 'Unknown Student',
      student_id: record.student_id?.student_id || 'N/A',
      class_name: record.student_id?.class_id?.name || 'Unknown Class',
      date: record.date ? new Date(record.date).toLocaleDateString() : 'N/A',
      status: record.status,
      remarks: record.remarks || '',
      marked_by: record.marked_by || 'System',
      marked_at: record.createdAt ? new Date(record.createdAt).toLocaleDateString() : ''
    }));

    // Create PDF using PDFKit
    const PDFDocument = require('pdfkit');
    const doc = new PDFDocument();

    // Set response headers
    res.setHeader('Content-Type', 'application/pdf');
    res.setHeader('Content-Disposition', 'attachment; filename=attendance-export.pdf');

    // Pipe the PDF to response
    doc.pipe(res);

    // Add content to PDF
    doc.fontSize(20).text('Attendance Report', 50, 50);
    doc.fontSize(12).text(`Generated on: ${new Date().toLocaleDateString()}`, 50, 80);

    let yPosition = 120;

    // Add headers
    doc.text('Student', 50, yPosition);
    doc.text('Class', 150, yPosition);
    doc.text('Date', 220, yPosition);
    doc.text('Status', 290, yPosition);
    doc.text('Remarks', 360, yPosition);

    yPosition += 20;

    // Add data
    formattedRecords.forEach(record => {
      if (yPosition > 750) {
        doc.addPage();
        yPosition = 50;
      }

      doc.text(record.student_name.substring(0, 15), 50, yPosition);
      doc.text(record.class_name.substring(0, 10), 150, yPosition);
      doc.text(record.date, 220, yPosition);
      doc.text(record.status, 290, yPosition);
      doc.text(record.remarks.substring(0, 15), 360, yPosition);

      yPosition += 15;
    });

    doc.end();
  } catch (error) {
    console.error('Error exporting attendance to PDF:', error);
    res.status(500).json({ message: 'Internal server error' , error:error });
  }
};

// Export attendance to Excel
const exportAttendanceExcel = async (req, res) => {
  try {
    const { school_id } = req.params;
    const { class_id, date, status } = req.query;

    if (!school_id) {
      return res.status(400).json({ message: 'School ID is required' });
    }

    // Convert school_id to ObjectId for proper matching
    const mongoose = require('mongoose');
    const schoolObjectId = new mongoose.Types.ObjectId(school_id);

    // Build filter object
    const filter = { school_id: schoolObjectId };
    if (class_id) filter.class_id = new mongoose.Types.ObjectId(class_id);
    if (date) filter.date = new Date(date);
    if (status) filter.status = status;

    // Get attendance records with populated data
    const attendanceRecords = await Attendance.find(filter)
      .populate({
        path: 'student_id',
        select: 'first_name last_name student_id class_id',
        populate: {
          path: 'class_id',
          select: 'name grade_level'
        }
      })
      .populate('class_schedule_id', 'period_id day_of_week')
      .sort({ date: -1, createdAt: -1 })
      .lean();

    // Format the response
    const formattedRecords = attendanceRecords.map(record => ({
      'Student Name': record.student_id ?
        `${record.student_id.first_name} ${record.student_id.last_name}` : 'Unknown Student',
      'Student ID': record.student_id?.student_id || 'N/A',
      'Class': record.student_id?.class_id?.name || 'Unknown Class',
      'Date': record.date ? new Date(record.date).toLocaleDateString() : 'N/A',
      'Status': record.status,
      'Remarks': record.remarks || '',
      'Marked By': record.marked_by || 'System',
      'Marked At': record.createdAt ? new Date(record.createdAt).toLocaleDateString() : ''
    }));

    // Create Excel file using xlsx
    const XLSX = require('xlsx');
    const workbook = XLSX.utils.book_new();
    const worksheet = XLSX.utils.json_to_sheet(formattedRecords);

    XLSX.utils.book_append_sheet(workbook, worksheet, 'Attendance');

    // Generate buffer
    const buffer = XLSX.write(workbook, { type: 'buffer', bookType: 'xlsx' });

    // Set response headers
    res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
    res.setHeader('Content-Disposition', 'attachment; filename=attendance-export.xlsx');

    res.send(buffer);
  } catch (error) {
    console.error('Error exporting attendance to Excel:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
};

// Create attendance records
const createAttendance = async (req, res) => {
  try {
    const { day_of_week, class_id, period_id, subject_id, date, academic_year, students, school_id } = req.body;
    const user = req.user;

    // Validation
    if (!day_of_week || !class_id || !period_id || !subject_id || !date || !academic_year || !students || !Array.isArray(students) || !school_id) {
      return res.status(400).json({
        message: 'Missing required fields: day_of_week, class_id, period_id, subject_id, date, academic_year, school_id, and students array'
      });
    }

    if (students.length === 0) {
      return res.status(400).json({ message: 'At least one student attendance record is required' });
    }



    // Find or create the schedule
    let schedule = await ClassSchedule.findOne({
      class_id,
      period_id,
      subject_id,
      day_of_week,
      school_id
    });

    if (!schedule) {
      // Create a basic schedule if it doesn't exist
      schedule = new ClassSchedule({
        class_id,
        period_id,
        subject_id,
        day_of_week,
        school_id,
        teacher_id: user._id, // Use current user as teacher
        schedule_type: 'Normal'
      });
      await schedule.save();
    }

    // Prepare attendance records
    const attendanceRecords = [];
    const errors = [];

    for (const studentData of students) {
      const { student_id, status } = studentData;

      if (!student_id || !status) {
        errors.push(`Missing student_id or status for student: ${student_id}`);
        continue;
      }

      // Check if student exists and belongs to the school
      const student = await Student.findOne({ _id: student_id, school_id });
      if (!student) {
        errors.push(`Student not found or doesn't belong to this school: ${student_id}`);
        continue;
      }

      // Check if attendance already exists for this student, date, and schedule
      const existingAttendance = await Attendance.findOne({
        student_id,
        schedule_id: schedule._id,
        date: new Date(date),
        school_id
      });

      if (existingAttendance) {
        errors.push(`Attendance already exists for student: ${student.first_name} ${student.last_name} on ${date}`);
        continue;
      }

      attendanceRecords.push({
        school_id,
        student_id,
        schedule_id: schedule._id,
        status,
        date: new Date(date),
        academic_year
      });
    }

    if (attendanceRecords.length === 0) {
      return res.status(400).json({
        message: 'No valid attendance records to create',
        errors
      });
    }

    // Create attendance records
    const createdRecords = await Attendance.insertMany(attendanceRecords);

    // Log activity
    await ActivityLog.logActivity({
      user_id: user._id,
      action: 'attendance_marked',
      target_type: 'attendance',
      target_id: schedule._id,
      details: `Created attendance for ${createdRecords.length} students`,
      school_id
    });

    res.status(201).json({
      message: `Successfully created attendance for ${createdRecords.length} students`,
      created_count: createdRecords.length,
      errors: errors.length > 0 ? errors : undefined,
      attendance_records: createdRecords
    });

  } catch (error) {
    console.error('Error creating attendance:', error);
    res.status(500).json({ message: 'Internal server error', error:error });
  }
};

// Update attendance record
const updateAttendance = async (req, res) => {
  try {
    const { id } = req.params;
    const { status, date, academic_year, school_id } = req.body;
    const user = req.user;

    if (!id) {
      return res.status(400).json({ message: 'Attendance ID is required' });
    }

    if (!school_id) {
      return res.status(400).json({ message: 'School ID is required' });
    }

    // Find the attendance record
    const attendance = await Attendance.findOne({
      _id: id,
      school_id: school_id
    });

    if (!attendance) {
      return res.status(404).json({ message: 'Attendance record not found' });
    }

    // Update fields
    if (status) attendance.status = status;
    if (date) attendance.date = new Date(date);
    if (academic_year) attendance.academic_year = academic_year;

    await attendance.save();

    // Log activity
    await ActivityLog.logActivity({
      user_id: user._id,
      action: 'attendance_updated',
      target_type: 'attendance',
      target_id: attendance._id,
      details: `Updated attendance status to ${attendance.status}`,
      school_id: school_id
    });

    res.status(200).json({
      message: 'Attendance updated successfully',
      attendance
    });

  } catch (error) {
    console.error('Error updating attendance:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
};

// Delete attendance record
const deleteAttendance = async (req, res) => {
  try {
    const { id } = req.params;
    const { school_id } = req.body;
    const user = req.user;

    if (!id) {
      return res.status(400).json({ message: 'Attendance ID is required' });
    }

    if (!school_id) {
      return res.status(400).json({ message: 'School ID is required' });
    }

    // Find and delete the attendance record
    const attendance = await Attendance.findOneAndDelete({
      _id: id,
      school_id: school_id
    });

    if (!attendance) {
      return res.status(404).json({ message: 'Attendance record not found' });
    }

    // Log activity
    await ActivityLog.logActivity({
      user_id: user._id,
      action: 'attendance_deleted',
      target_type: 'attendance',
      target_id: attendance._id,
      details: `Deleted attendance record`,
      school_id: school_id
    });

    res.status(200).json({
      message: 'Attendance deleted successfully'
    });

  } catch (error) {
    console.error('Error deleting attendance:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
};

// Delete multiple attendance records
const deleteMultipleAttendances = async (req, res) => {
  try {
    const { ids, school_id } = req.body;
    const user = req.user;

    if (!ids || !Array.isArray(ids) || ids.length === 0) {
      return res.status(400).json({ message: 'Array of attendance IDs is required' });
    }

    if (!school_id) {
      return res.status(400).json({ message: 'School ID is required' });
    }

    // Delete attendance records
    const result = await Attendance.deleteMany({
      _id: { $in: ids },
      school_id: school_id
    });

    // Log activity
    await ActivityLog.logActivity({
      user_id: user._id,
      action: 'attendance_bulk_deleted',
      target_type: 'attendance',
      details: `Deleted ${result.deletedCount} attendance records`,
      school_id: school_id
    });

    res.status(200).json({
      message: `Successfully deleted ${result.deletedCount} attendance records`,
      deleted_count: result.deletedCount
    });

  } catch (error) {
    console.error('Error deleting multiple attendances:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
};

module.exports = {
  getAttendanceRecords,
  getAttendanceStats,
  exportAttendancePDF,
  exportAttendanceExcel,
  createAttendance,
  updateAttendance,
  deleteAttendance,
  deleteMultipleAttendances
};
