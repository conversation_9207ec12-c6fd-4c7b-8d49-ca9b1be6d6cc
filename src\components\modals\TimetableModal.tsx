"use client";

import React, { useState, useEffect } from "react";
import { X, Clock4, Save, Search, ChevronDown } from "lucide-react";
import { motion, AnimatePresence } from "framer-motion";

interface TimetableModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (data: any) => Promise<void>;
  schedule?: any | null;
  classes: any[];
  subjects: any[];
  teachers: any[];
  periods: any[];
  loading?: boolean;
  preSelectedClass?: string;
  isClassLocked?: boolean;
  isExamMode?: boolean;
}

export default function TimetableModal({
  isOpen,
  onClose,
  onSubmit,
  schedule,
  classes,
  subjects,
  teachers,
  periods,
  loading = false,
  preSelectedClass,
  isClassLocked = false,
  isExamMode = false
}: TimetableModalProps) {
  const [formData, setFormData] = useState({
    class_id: "",
    subject_id: "",
    teacher_id: "",
    period_id: "",
    day_of_week: "Monday" as "Monday" | "Tuesday" | "Wednesday" | "Thursday" | "Friday",
    schedule_type: "Normal" as "Normal" | "Exam" | "Special"
  });
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [teacherSearch, setTeacherSearch] = useState("");
  const [classSearch, setClassSearch] = useState("");
  const [subjectSearch, setSubjectSearch] = useState("");

  // Dropdown visibility states
  const [showClassDropdown, setShowClassDropdown] = useState(false);
  const [showSubjectDropdown, setShowSubjectDropdown] = useState(false);
  const [showTeacherDropdown, setShowTeacherDropdown] = useState(false);

  const isEditing = !!schedule;
  const days = ["Monday", "Tuesday", "Wednesday", "Thursday", "Friday"];

  useEffect(() => {
    if (isOpen) {
      if (schedule) {
        setFormData({
          class_id: schedule.class_id || "",
          subject_id: schedule.subject_id || "",
          teacher_id: schedule.teacher_id || "", // Keep teacher/supervisor in both modes
          period_id: schedule.period_id || "",
          day_of_week: schedule.day_of_week || "Monday",
          schedule_type: schedule.schedule_type || "Normal"
        });
      } else {
        setFormData({
          class_id: preSelectedClass || "",
          subject_id: "",
          teacher_id: "",
          period_id: "",
          day_of_week: "Monday",
          schedule_type: "Normal"
        });
      }
      setErrors({});
      setTeacherSearch("");
      setClassSearch("");
      setSubjectSearch("");
      setShowClassDropdown(false);
      setShowSubjectDropdown(false);
      setShowTeacherDropdown(false);
    }
  }, [isOpen, schedule, preSelectedClass, isExamMode]);

  // Note: Removed the useEffect that clears teacher when switching to exam mode
  // since we now allow teacher selection in exam mode for supervision

  // Clear subject when teacher changes (since available subjects depend on teacher)
  useEffect(() => {
    if (formData.teacher_id && formData.subject_id) {
      const availableSubjects = getAvailableSubjectsForTeacher();
      const isSubjectStillValid = availableSubjects.some(subject => subject._id === formData.subject_id);

      if (!isSubjectStillValid) {
        setFormData(prev => ({ ...prev, subject_id: "" }));
        setSubjectSearch("");
      }
    } else if (!formData.teacher_id && formData.subject_id) {
      // Clear subject if no teacher is selected
      setFormData(prev => ({ ...prev, subject_id: "" }));
      setSubjectSearch("");
    }
  }, [formData.teacher_id, formData.class_id]);

  // Clear teacher and subject when class changes (if teacher has no assignments for new class)
  useEffect(() => {
    if (formData.class_id && formData.teacher_id) {
      if (!hasTeacherAssignmentForClass()) {
        setFormData(prev => ({
          ...prev,
          teacher_id: "",
          subject_id: ""
        }));
        setTeacherSearch("");
        setSubjectSearch("");
      }
    }
  }, [formData.class_id]);

  // Close dropdowns when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as HTMLElement;

      // Don't close if clicking inside any dropdown or search input
      if (target.closest('.dropdown-container') || target.closest('.search-input')) {
        return;
      }

      setShowClassDropdown(false);
      setShowSubjectDropdown(false);
      setShowTeacherDropdown(false);
    };

    if (showClassDropdown || showSubjectDropdown || showTeacherDropdown) {
      document.addEventListener('click', handleClickOutside);
      return () => document.removeEventListener('click', handleClickOutside);
    }
  }, [showClassDropdown, showSubjectDropdown, showTeacherDropdown]);

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.class_id) {
      newErrors.class_id = "Please select a class to continue";
    }

    // Teacher/Supervisor is required in both modes
    if (!formData.teacher_id) {
      newErrors.teacher_id = isExamMode
        ? "Please select an exam supervisor. Any teacher can supervise exams."
        : "Please select a teacher who is assigned to this class";
    }

    // Additional teacher validation: check if teacher has assignments for the class (only in normal mode)
    if (!isExamMode && formData.teacher_id && formData.class_id && !hasTeacherAssignmentForClass()) {
      newErrors.teacher_id = "This teacher has no subject assignments for the selected class. Please choose a different teacher or create teacher assignments first.";
    }

    if (!formData.subject_id) {
      newErrors.subject_id = isExamMode
        ? "Please select the subject for the exam"
        : "Please select a subject from the teacher's assignments";
    } else if (!isExamMode && formData.teacher_id && formData.class_id) {
      // Validate that the selected subject is available for the teacher
      const availableSubjects = getAvailableSubjectsForTeacher();
      const isSubjectValid = availableSubjects.some(subject => subject._id === formData.subject_id);
      if (!isSubjectValid) {
        newErrors.subject_id = "This subject is not assigned to the selected teacher for this class. Please choose a different subject or update teacher assignments.";
      }
    }

    if (!formData.period_id) {
      newErrors.period_id = "Please select a time period for this " + (isExamMode ? "exam" : "class");
    }
    if (!formData.day_of_week) {
      newErrors.day_of_week = "Please select the day of the week";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) return;

    setIsSubmitting(true);
    try {
      await onSubmit(formData);
      onClose();
    } catch (error) {
      console.error("Error submitting schedule:", error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));

    // Clear errors for this field
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: "" }));
    }

    // Special handling for field changes that affect other fields
    if (field === "teacher_id" && value) {
      // Clear subject when teacher changes
      setFormData(prev => ({ ...prev, subject_id: "" }));
      setSubjectSearch("");
      // Clear subject error if it exists
      if (errors.subject_id) {
        setErrors(prev => ({ ...prev, subject_id: "" }));
      }
    }

    if (field === "class_id" && value) {
      // Clear teacher and subject when class changes
      setFormData(prev => ({
        ...prev,
        teacher_id: "",
        subject_id: ""
      }));
      setTeacherSearch("");
      setSubjectSearch("");
      // Clear related errors
      if (errors.teacher_id || errors.subject_id) {
        setErrors(prev => ({
          ...prev,
          teacher_id: "",
          subject_id: ""
        }));
      }
    }
  };

  const getTeacherDisplay = (teacher: any) => {
    return teacher.first_name && teacher.last_name ?
      `${teacher.first_name} ${teacher.last_name}` :
      teacher.name || 'Unknown Teacher';
  };

  // Helper functions for getting selected names
  const getSelectedClassName = () => {
    const selectedClass = classes.find(c => c._id === formData.class_id);
    return selectedClass ? selectedClass.name : '';
  };

  const getSelectedSubjectName = () => {
    const selectedSubject = subjects.find(s => s._id === formData.subject_id);
    return selectedSubject ? selectedSubject.name : '';
  };

  const getSelectedTeacherName = () => {
    const selectedTeacher = teachers.find(t => t._id === formData.teacher_id);
    return selectedTeacher ? getTeacherDisplay(selectedTeacher) : '';
  };

  // Check if teacher has assignments for the selected class
  const hasTeacherAssignmentForClass = () => {
    if (!formData.teacher_id || !formData.class_id) {
      return false;
    }

    const selectedTeacher = teachers.find(t => t._id === formData.teacher_id);
    if (!selectedTeacher || !selectedTeacher.assignments) {
      return false;
    }

    return selectedTeacher.assignments.some(
      assignment => assignment.class_id === formData.class_id
    );
  };

  // Get subjects that the selected teacher can teach for the selected class
  const getAvailableSubjectsForTeacher = () => {
    if (!formData.teacher_id || !formData.class_id) {
      return [];
    }

    const selectedTeacher = teachers.find(t => t._id === formData.teacher_id);
    if (!selectedTeacher || !selectedTeacher.assignments) {
      return [];
    }

    // Find the assignment for the selected class
    const classAssignment = selectedTeacher.assignments.find(
      assignment => assignment.class_id === formData.class_id
    );

    if (!classAssignment) {
      return [];
    }

    // Filter subjects based on the teacher's assignments for this class
    return subjects.filter(subject =>
      classAssignment.subjects.includes(subject.name)
    );
  };

  const getPeriodDisplay = (period: any) => {
    return `Period ${period.period_number} (${period.start_time.slice(0, 5)} - ${period.end_time.slice(0, 5)})`;
  };

  // Filter teachers based on search
  const filteredTeachers = teachers.filter(teacher => {
    const teacherName = getTeacherDisplay(teacher).toLowerCase();
    return teacherName.includes(teacherSearch.toLowerCase());
  });

  // Filter classes based on search
  const filteredClasses = classes.filter(classItem => {
    const className = classItem.name.toLowerCase();
    return className.includes(classSearch.toLowerCase());
  });

  // Filter subjects based on teacher assignment and search
  const filteredSubjects = (() => {
    // In exam mode, show all subjects (no teacher restriction)
    if (isExamMode) {
      return subjects.filter(subject => {
        const subjectName = subject.name.toLowerCase();
        return subjectName.includes(subjectSearch.toLowerCase());
      });
    }

    // In normal mode, get subjects available for the selected teacher and class
    const availableSubjects = getAvailableSubjectsForTeacher();

    // If no teacher is selected, return empty array (teacher must be selected first)
    if (!formData.teacher_id) {
      return [];
    }

    // Filter available subjects based on search
    return availableSubjects.filter(subject => {
      const subjectName = subject.name.toLowerCase();
      return subjectName.includes(subjectSearch.toLowerCase());
    });
  })();

  return (
    <AnimatePresence>
      {isOpen && (
        <div className="fixed inset-0 z-50 flex items-center justify-center">
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="absolute inset-0 bg-black bg-opacity-50"
            onClick={onClose}
          />
          
          <motion.div
            initial={{ opacity: 0, scale: 0.95, y: 20 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.95, y: 20 }}
            className="relative bg-white dark:bg-gray-800 rounded-lg shadow-xl w-full max-w-lg mx-4 max-h-[90vh] overflow-y-auto"
          >
            {/* Header */}
            <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 bg-indigo-500 rounded-lg flex items-center justify-center">
                  <Clock4 className="h-5 w-5 text-white" />
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                    {isEditing
                      ? (isExamMode ? "Edit Exam" : "Edit Schedule")
                      : (isExamMode ? "Add Exam Entry" : "Add Schedule Entry")
                    }
                  </h3>
                  <p className="text-sm text-gray-500 dark:text-gray-400">
                    {isEditing
                      ? (isExamMode ? "Update exam entry" : "Update schedule entry")
                      : (isExamMode ? "Create new exam entry" : "Create new schedule entry")
                    }
                  </p>
                </div>
              </div>
              <button
                onClick={onClose}
                className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
              >
                <X className="h-5 w-5" />
              </button>
            </div>

            {/* Form */}
            <form onSubmit={handleSubmit} className="p-6 space-y-4">
              {/* Class */}
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Class {isClassLocked && <span className="text-xs text-gray-500">(Locked)</span>}
                </label>
                <div className="relative">
                  <div
                    className={`w-full px-3 py-2 border rounded-md cursor-pointer flex items-center justify-between ${
                      isClassLocked ? "bg-gray-100 dark:bg-gray-600 cursor-not-allowed" : "bg-white dark:bg-gray-700"
                    } ${
                      errors.class_id
                        ? "border-red-500 dark:border-red-500"
                        : "border-gray-300 dark:border-gray-600"
                    }`}
                    onClick={(e) => {
                      if (!isClassLocked) {
                        e.stopPropagation();
                        setShowClassDropdown(!showClassDropdown);
                        setShowSubjectDropdown(false);
                        setShowTeacherDropdown(false);
                      }
                    }}
                  >
                    <span className="text-gray-900 dark:text-white">
                      {getSelectedClassName() || "Select class"}
                    </span>
                    {!isClassLocked && <ChevronDown className="h-4 w-4 text-gray-400" />}
                  </div>

                  {!isClassLocked && showClassDropdown && (
                    <div className="dropdown-container absolute z-10 w-full mt-1 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md shadow-lg max-h-60 overflow-hidden">
                      <div className="p-2 border-b border-gray-200 dark:border-gray-600">
                        <div className="relative">
                          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                          <input
                            type="text"
                            placeholder="Search classes..."
                            value={classSearch}
                            onChange={(e) => setClassSearch(e.target.value)}
                            className="search-input w-full pl-10 pr-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 dark:bg-gray-600 dark:text-white text-sm"
                            onClick={(e) => e.stopPropagation()}
                            onFocus={(e) => e.stopPropagation()}
                          />
                        </div>
                      </div>
                      <div className="max-h-48 overflow-y-auto">
                        {filteredClasses.length > 0 ? (
                          filteredClasses.map((classItem) => (
                            <div
                              key={classItem._id}
                              className="px-3 py-2 hover:bg-gray-100 dark:hover:bg-gray-600 cursor-pointer text-gray-900 dark:text-white"
                              onClick={(e) => {
                                e.stopPropagation();
                                handleInputChange("class_id", classItem._id);
                                setShowClassDropdown(false);
                                setClassSearch('');
                              }}
                            >
                              {classItem.name}
                            </div>
                          ))
                        ) : (
                          <div className="px-3 py-2 text-gray-500 dark:text-gray-400 text-sm">
                            No classes found
                          </div>
                        )}
                      </div>
                    </div>
                  )}
                </div>
                {errors.class_id && (
                  <p className="mt-1 text-sm text-red-500">{errors.class_id}</p>
                )}
              </div>

              {/* Teacher */}
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  {isExamMode ? 'Exam Supervisor' : 'Teacher'} (Required)
                </label>

                {isExamMode && (
                  <div className="mb-3 p-3 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-md">
                    <p className="text-sm text-blue-700 dark:text-blue-300">
                      <strong>Exam Supervision:</strong> Select a teacher to supervise this exam.
                      The supervisor doesn't need to teach this subject - any available teacher can supervise any exam.
                    </p>
                  </div>
                )}

                <div className="relative">
                  <div
                    className={`w-full px-3 py-2 border rounded-md cursor-pointer flex items-center justify-between bg-white dark:bg-gray-700 ${
                      errors.teacher_id
                        ? "border-red-500 dark:border-red-500"
                        : "border-gray-300 dark:border-gray-600"
                    }`}
                    onClick={(e) => {
                      e.stopPropagation();
                      setShowTeacherDropdown(!showTeacherDropdown);
                      setShowClassDropdown(false);
                      setShowSubjectDropdown(false);
                    }}
                  >
                    <span className="text-gray-900 dark:text-white">
                      {getSelectedTeacherName() || (isExamMode ? "Select exam supervisor" : "Select teacher")}
                    </span>
                    <ChevronDown className="h-4 w-4 text-gray-400" />
                  </div>

                  {showTeacherDropdown && (
                    <div className="dropdown-container absolute z-10 w-full mt-1 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md shadow-lg max-h-60 overflow-hidden">
                      <div className="p-2 border-b border-gray-200 dark:border-gray-600">
                        <div className="relative">
                          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                          <input
                            type="text"
                            placeholder={isExamMode ? "Search supervisors..." : "Search teachers..."}
                            value={teacherSearch}
                            onChange={(e) => setTeacherSearch(e.target.value)}
                            className="search-input w-full pl-10 pr-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 dark:bg-gray-600 dark:text-white text-sm"
                            onClick={(e) => e.stopPropagation()}
                            onFocus={(e) => e.stopPropagation()}
                          />
                        </div>
                      </div>
                      <div className="max-h-48 overflow-y-auto">
                        {filteredTeachers.length > 0 ? (
                          filteredTeachers.map((teacher) => (
                            <div
                              key={teacher._id}
                              className="px-3 py-2 hover:bg-gray-100 dark:hover:bg-gray-600 cursor-pointer text-gray-900 dark:text-white"
                              onClick={(e) => {
                                e.stopPropagation();
                                handleInputChange("teacher_id", teacher._id);
                                setShowTeacherDropdown(false);
                                setTeacherSearch('');
                              }}
                            >
                              {getTeacherDisplay(teacher)}
                            </div>
                          ))
                        ) : (
                          <div className="px-3 py-2 text-gray-500 dark:text-gray-400 text-sm">
                            {isExamMode ? "No supervisors found" : "No teachers found"}
                          </div>
                        )}
                      </div>
                    </div>
                  )}
                </div>
                {errors.teacher_id && (
                  <p className="mt-1 text-sm text-red-500">{errors.teacher_id}</p>
                )}
              </div>

              {/* Subject */}
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Subject
                </label>

                {/* Info message when no teacher is selected (only in normal mode) */}
                {!isExamMode && !formData.teacher_id && (
                  <div className="mb-3 p-3 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-md">
                    <div className="flex items-start space-x-2">
                      <div className="w-4 h-4 bg-blue-500 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                        <span className="text-white text-xs font-bold">i</span>
                      </div>
                      <div>
                        <p className="text-sm text-blue-700 dark:text-blue-300 font-medium">
                          Select a teacher first
                        </p>
                        <p className="text-xs text-blue-600 dark:text-blue-400 mt-1">
                          Available subjects will be filtered based on the teacher's assignments for this class.
                        </p>
                      </div>
                    </div>
                  </div>
                )}

                {/* Info message for exam mode */}
                {isExamMode && !formData.teacher_id && (
                  <div className="mb-3 p-3 bg-orange-50 dark:bg-orange-900/20 border border-orange-200 dark:border-orange-800 rounded-md">
                    <div className="flex items-start space-x-2">
                      <div className="w-4 h-4 bg-orange-500 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                        <span className="text-white text-xs font-bold">!</span>
                      </div>
                      <div>
                        <p className="text-sm text-orange-700 dark:text-orange-300 font-medium">
                          Exam Supervision Mode
                        </p>
                        <p className="text-xs text-orange-600 dark:text-orange-400 mt-1">
                          Any teacher can supervise exams, regardless of their subject assignments. All subjects are available for selection.
                        </p>
                      </div>
                    </div>
                  </div>
                )}

                {/* Warning when teacher has no assignments for this class (only in normal mode) */}
                {!isExamMode && formData.teacher_id && formData.class_id && !hasTeacherAssignmentForClass() && (
                  <div className="mb-3 p-3 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-md">
                    <div className="flex items-start space-x-2">
                      <div className="w-4 h-4 bg-yellow-500 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                        <span className="text-white text-xs font-bold">⚠</span>
                      </div>
                      <div>
                        <p className="text-sm text-yellow-700 dark:text-yellow-300 font-medium">
                          No assignments found
                        </p>
                        <p className="text-xs text-yellow-600 dark:text-yellow-400 mt-1">
                          The selected teacher has no subject assignments for this class. You may need to create teacher assignments first, or select a different teacher.
                        </p>
                      </div>
                    </div>
                  </div>
                )}

                <div className="relative">
                  <div
                    className={`w-full px-3 py-2 border rounded-md cursor-pointer flex items-center justify-between ${
                      (!formData.teacher_id && !isExamMode) ? "bg-gray-100 dark:bg-gray-600 cursor-not-allowed" : "bg-white dark:bg-gray-700"
                    } ${
                      errors.subject_id
                        ? "border-red-500 dark:border-red-500"
                        : "border-gray-300 dark:border-gray-600"
                    }`}
                    onClick={(e) => {
                      if (formData.teacher_id || isExamMode) {
                        e.stopPropagation();
                        setShowSubjectDropdown(!showSubjectDropdown);
                        setShowClassDropdown(false);
                        setShowTeacherDropdown(false);
                      }
                    }}
                  >
                    <span className={`${(!formData.teacher_id && !isExamMode) ? 'text-gray-500 dark:text-gray-400' : 'text-gray-900 dark:text-white'}`}>
                      {(!formData.teacher_id && !isExamMode)
                        ? "Select teacher first"
                        : (getSelectedSubjectName() || "Select subject")
                      }
                    </span>
                    {(formData.teacher_id || isExamMode) && <ChevronDown className="h-4 w-4 text-gray-400" />}
                  </div>

                  {(formData.teacher_id || isExamMode) && showSubjectDropdown && (
                    <div className="dropdown-container absolute z-10 w-full mt-1 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md shadow-lg max-h-60 overflow-hidden">
                      <div className="p-2 border-b border-gray-200 dark:border-gray-600">
                        <div className="relative">
                          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                          <input
                            type="text"
                            placeholder="Search subjects..."
                            value={subjectSearch}
                            onChange={(e) => setSubjectSearch(e.target.value)}
                            className="search-input w-full pl-10 pr-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 dark:bg-gray-600 dark:text-white text-sm"
                            onClick={(e) => e.stopPropagation()}
                            onFocus={(e) => e.stopPropagation()}
                          />
                        </div>
                      </div>
                      <div className="max-h-48 overflow-y-auto">
                        {filteredSubjects.length > 0 ? (
                          filteredSubjects.map((subject) => (
                            <div
                              key={subject._id}
                              className="px-3 py-2 hover:bg-gray-100 dark:hover:bg-gray-600 cursor-pointer text-gray-900 dark:text-white"
                              onClick={(e) => {
                                e.stopPropagation();
                                handleInputChange("subject_id", subject._id);
                                setShowSubjectDropdown(false);
                                setSubjectSearch('');
                              }}
                            >
                              {subject.name}
                            </div>
                          ))
                        ) : (
                          <div className="px-3 py-2 text-gray-500 dark:text-gray-400 text-sm">
                            {subjectSearch
                              ? "No subjects match your search"
                              : "No subjects assigned to this teacher for this class"
                            }
                          </div>
                        )}
                      </div>
                    </div>
                  )}
                </div>
                {errors.subject_id && (
                  <p className="mt-1 text-sm text-red-500">{errors.subject_id}</p>
                )}
              </div>



              {/* Day and Period */}
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Day of Week
                  </label>
                  <select
                    value={formData.day_of_week}
                    onChange={(e) => handleInputChange("day_of_week", e.target.value)}
                    className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 dark:bg-gray-700 dark:text-white ${
                      errors.day_of_week 
                        ? "border-red-500 dark:border-red-500" 
                        : "border-gray-300 dark:border-gray-600"
                    }`}
                  >
                    {days.map((day) => (
                      <option key={day} value={day}>
                        {day}
                      </option>
                    ))}
                  </select>
                  {errors.day_of_week && (
                    <p className="mt-1 text-sm text-red-500">{errors.day_of_week}</p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Period
                  </label>
                  <select
                    value={formData.period_id}
                    onChange={(e) => handleInputChange("period_id", e.target.value)}
                    className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 dark:bg-gray-700 dark:text-white ${
                      errors.period_id 
                        ? "border-red-500 dark:border-red-500" 
                        : "border-gray-300 dark:border-gray-600"
                    }`}
                  >
                    <option value="">Select period</option>
                    {periods.map((period) => (
                      <option key={period._id} value={period._id}>
                        {getPeriodDisplay(period)}
                      </option>
                    ))}
                  </select>
                  {errors.period_id && (
                    <p className="mt-1 text-sm text-red-500">{errors.period_id}</p>
                  )}
                </div>
              </div>

              {/* Schedule Type */}
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Schedule Type
                </label>
                <select
                  value={formData.schedule_type}
                  onChange={(e) => handleInputChange("schedule_type", e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 dark:bg-gray-700 dark:text-white"
                >
                  <option value="Normal">Normal</option>
                  <option value="Exam">Exam</option>
                  <option value="Special">Special</option>
                </select>
              </div>

              {/* Actions */}
              <div className="flex justify-end space-x-3 pt-4">
                <button
                  type="button"
                  onClick={onClose}
                  className="px-4 py-2 text-gray-700 dark:text-gray-300 border border-gray-300 dark:border-gray-600 rounded-md hover:bg-gray-50 dark:hover:bg-gray-700"
                  disabled={isSubmitting}
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  disabled={isSubmitting}
                  className="px-4 py-2 bg-indigo-500 text-white rounded-md hover:bg-indigo-600 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2"
                >
                  {isSubmitting ? (
                    <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                  ) : (
                    <Save className="h-4 w-4" />
                  )}
                  <span>{isSubmitting ? "Saving..." : (isEditing ? "Update" : "Create")}</span>
                </button>
              </div>
            </form>
          </motion.div>
        </div>
      )}
    </AnimatePresence>
  );
}
