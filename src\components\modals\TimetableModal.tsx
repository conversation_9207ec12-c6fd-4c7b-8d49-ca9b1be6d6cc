"use client";

import React, { useState, useEffect } from "react";
import { X, Clock4, Save } from "lucide-react";
import { motion, AnimatePresence } from "framer-motion";

interface TimetableModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (data: any) => Promise<void>;
  schedule?: any | null;
  classes: any[];
  subjects: any[];
  teachers: any[];
  periods: any[];
  loading?: boolean;
  preSelectedClass?: string;
  isClassLocked?: boolean;
  isExamMode?: boolean;
}

export default function TimetableModal({
  isOpen,
  onClose,
  onSubmit,
  schedule,
  classes,
  subjects,
  teachers,
  periods,
  loading = false,
  preSelectedClass,
  isClassLocked = false,
  isExamMode = false
}: TimetableModalProps) {
  const [formData, setFormData] = useState({
    class_id: "",
    subject_id: "",
    teacher_id: "",
    period_id: "",
    day_of_week: "Monday" as "Monday" | "Tuesday" | "Wednesday" | "Thursday" | "Friday",
    schedule_type: "Normal" as "Normal" | "Exam" | "Special"
  });
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [teacherSearch, setTeacherSearch] = useState("");
  const [classSearch, setClassSearch] = useState("");
  const [subjectSearch, setSubjectSearch] = useState("");

  // Dropdown visibility states
  const [showClassDropdown, setShowClassDropdown] = useState(false);
  const [showSubjectDropdown, setShowSubjectDropdown] = useState(false);
  const [showTeacherDropdown, setShowTeacherDropdown] = useState(false);

  const isEditing = !!schedule;
  const days = ["Monday", "Tuesday", "Wednesday", "Thursday", "Friday"];

  useEffect(() => {
    if (isOpen) {
      if (schedule) {
        setFormData({
          class_id: schedule.class_id || "",
          subject_id: schedule.subject_id || "",
          teacher_id: isExamMode ? "" : (schedule.teacher_id || ""), // Clear teacher in exam mode
          period_id: schedule.period_id || "",
          day_of_week: schedule.day_of_week || "Monday",
          schedule_type: schedule.schedule_type || "Normal"
        });
      } else {
        setFormData({
          class_id: preSelectedClass || "",
          subject_id: "",
          teacher_id: "",
          period_id: "",
          day_of_week: "Monday",
          schedule_type: "Normal"
        });
      }
      setErrors({});
      setTeacherSearch("");
      setClassSearch("");
      setSubjectSearch("");
    }
  }, [isOpen, schedule, preSelectedClass, isExamMode]);

  // Clear teacher when switching to exam mode
  useEffect(() => {
    if (isExamMode && formData.teacher_id) {
      setFormData(prev => ({ ...prev, teacher_id: "" }));
      setTeacherSearch("");
    }
  }, [isExamMode]);

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.class_id) {
      newErrors.class_id = "Class is required";
    }
    if (!formData.subject_id) {
      newErrors.subject_id = "Subject is required";
    }
    if (!formData.period_id) {
      newErrors.period_id = "Period is required";
    }
    if (!formData.day_of_week) {
      newErrors.day_of_week = "Day of week is required";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) return;

    setIsSubmitting(true);
    try {
      await onSubmit(formData);
      onClose();
    } catch (error) {
      console.error("Error submitting schedule:", error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: "" }));
    }
  };

  const getTeacherDisplay = (teacher: any) => {
    return teacher.first_name && teacher.last_name ?
      `${teacher.first_name} ${teacher.last_name}` :
      teacher.name || 'Unknown Teacher';
  };

  const getPeriodDisplay = (period: any) => {
    return `Period ${period.period_number} (${period.start_time.slice(0, 5)} - ${period.end_time.slice(0, 5)})`;
  };

  // Filter teachers based on search
  const filteredTeachers = teachers.filter(teacher => {
    const teacherName = getTeacherDisplay(teacher).toLowerCase();
    return teacherName.includes(teacherSearch.toLowerCase());
  });

  // Filter classes based on search
  const filteredClasses = classes.filter(classItem => {
    const className = classItem.name.toLowerCase();
    return className.includes(classSearch.toLowerCase());
  });

  // Filter subjects based on search
  const filteredSubjects = subjects.filter(subject => {
    const subjectName = subject.name.toLowerCase();
    return subjectName.includes(subjectSearch.toLowerCase());
  });

  return (
    <AnimatePresence>
      {isOpen && (
        <div className="fixed inset-0 z-50 flex items-center justify-center">
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="absolute inset-0 bg-black bg-opacity-50"
            onClick={onClose}
          />
          
          <motion.div
            initial={{ opacity: 0, scale: 0.95, y: 20 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.95, y: 20 }}
            className="relative bg-white dark:bg-gray-800 rounded-lg shadow-xl w-full max-w-lg mx-4 max-h-[90vh] overflow-y-auto"
          >
            {/* Header */}
            <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 bg-indigo-500 rounded-lg flex items-center justify-center">
                  <Clock4 className="h-5 w-5 text-white" />
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                    {isEditing
                      ? (isExamMode ? "Edit Exam" : "Edit Schedule")
                      : (isExamMode ? "Add Exam Entry" : "Add Schedule Entry")
                    }
                  </h3>
                  <p className="text-sm text-gray-500 dark:text-gray-400">
                    {isEditing
                      ? (isExamMode ? "Update exam entry" : "Update schedule entry")
                      : (isExamMode ? "Create new exam entry" : "Create new schedule entry")
                    }
                  </p>
                </div>
              </div>
              <button
                onClick={onClose}
                className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
              >
                <X className="h-5 w-5" />
              </button>
            </div>

            {/* Form */}
            <form onSubmit={handleSubmit} className="p-6 space-y-4">
              {/* Class */}
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Class {isClassLocked && <span className="text-xs text-gray-500">(Locked)</span>}
                </label>
                {!isClassLocked && (
                  <div className="relative">
                    <input
                      type="text"
                      placeholder="Search and select class..."
                      value={classSearch}
                      onChange={(e) => {
                        setClassSearch(e.target.value);
                        setShowClassDropdown(e.target.value.length > 0);
                      }}
                      onFocus={() => setShowClassDropdown(classSearch.length > 0)}
                      onBlur={() => setTimeout(() => setShowClassDropdown(false), 150)}
                      className={`w-full px-3 py-2 mb-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 dark:bg-gray-700 dark:text-white text-sm ${
                        errors.class_id
                          ? "border-red-500 dark:border-red-500"
                          : "border-gray-300 dark:border-gray-600"
                      }`}
                    />
                    {showClassDropdown && filteredClasses.length > 0 && (
                      <div className="absolute z-10 w-full mt-1 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md shadow-lg max-h-40 overflow-y-auto">
                        {filteredClasses.map((classItem) => (
                          <button
                            key={classItem._id}
                            type="button"
                            onClick={() => {
                              handleInputChange("class_id", classItem._id);
                              setClassSearch(classItem.name);
                              setShowClassDropdown(false);
                            }}
                            className="w-full px-3 py-2 text-left hover:bg-gray-100 dark:hover:bg-gray-600 focus:bg-gray-100 dark:focus:bg-gray-600 focus:outline-none text-sm"
                          >
                            {classItem.name}
                          </button>
                        ))}
                      </div>
                    )}
                  </div>
                )}
                <select
                  value={formData.class_id}
                  onChange={(e) => {
                    handleInputChange("class_id", e.target.value);
                    if (!isClassLocked) {
                      const selectedClass = classes.find(c => c._id === e.target.value);
                      if (selectedClass) {
                        setClassSearch(selectedClass.name);
                      } else {
                        setClassSearch("");
                      }
                    }
                  }}
                  disabled={isClassLocked}
                  className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 dark:bg-gray-700 dark:text-white ${
                    errors.class_id
                      ? "border-red-500 dark:border-red-500"
                      : "border-gray-300 dark:border-gray-600"
                  } ${isClassLocked ? "bg-gray-100 dark:bg-gray-600 cursor-not-allowed" : ""}`}
                >
                  <option value="">Select class</option>
                  {classes.map((classItem) => (
                    <option key={classItem._id} value={classItem._id}>
                      {classItem.name}
                    </option>
                  ))}
                </select>
                {errors.class_id && (
                  <p className="mt-1 text-sm text-red-500">{errors.class_id}</p>
                )}
              </div>

              {/* Subject */}
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Subject
                </label>
                <div className="relative">
                  <input
                    type="text"
                    placeholder="Search and select subject..."
                    value={subjectSearch}
                    onChange={(e) => {
                      setSubjectSearch(e.target.value);
                      setShowSubjectDropdown(e.target.value.length > 0);
                    }}
                    onFocus={() => setShowSubjectDropdown(subjectSearch.length > 0)}
                    onBlur={() => setTimeout(() => setShowSubjectDropdown(false), 150)}
                    className={`w-full px-3 py-2 mb-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 dark:bg-gray-700 dark:text-white text-sm ${
                      errors.subject_id
                        ? "border-red-500 dark:border-red-500"
                        : "border-gray-300 dark:border-gray-600"
                    }`}
                  />
                  {showSubjectDropdown && filteredSubjects.length > 0 && (
                    <div className="absolute z-10 w-full mt-1 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md shadow-lg max-h-40 overflow-y-auto">
                      {filteredSubjects.map((subject) => (
                        <button
                          key={subject._id}
                          type="button"
                          onClick={() => {
                            handleInputChange("subject_id", subject._id);
                            setSubjectSearch(subject.name);
                            setShowSubjectDropdown(false);
                          }}
                          className="w-full px-3 py-2 text-left hover:bg-gray-100 dark:hover:bg-gray-600 focus:bg-gray-100 dark:focus:bg-gray-600 focus:outline-none text-sm"
                        >
                          {subject.name}
                        </button>
                      ))}
                    </div>
                  )}
                </div>
                <select
                  value={formData.subject_id}
                  onChange={(e) => {
                    handleInputChange("subject_id", e.target.value);
                    const selectedSubject = subjects.find(s => s._id === e.target.value);
                    if (selectedSubject) {
                      setSubjectSearch(selectedSubject.name);
                    } else {
                      setSubjectSearch("");
                    }
                  }}
                  className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 dark:bg-gray-700 dark:text-white ${
                    errors.subject_id
                      ? "border-red-500 dark:border-red-500"
                      : "border-gray-300 dark:border-gray-600"
                  }`}
                >
                  <option value="">Select subject</option>
                  {subjects.map((subject) => (
                    <option key={subject._id} value={subject._id}>
                      {subject.name}
                    </option>
                  ))}
                </select>
                {errors.subject_id && (
                  <p className="mt-1 text-sm text-red-500">{errors.subject_id}</p>
                )}
              </div>

              {/* Teacher */}
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  {isExamMode ? 'Exam Supervisor' : 'Teacher'} (Optional)
                </label>

                {isExamMode && (
                  <div className="mb-3 p-3 bg-orange-50 dark:bg-orange-900/20 border border-orange-200 dark:border-orange-800 rounded-md">
                    <p className="text-sm text-orange-700 dark:text-orange-300">
                      <strong>Exam Mode:</strong> Teacher assignments are typically managed separately for exams.
                      You can leave this field empty and assign supervisors through the exam management system.
                    </p>
                  </div>
                )}

                <div className="relative">
                  <input
                    type="text"
                    placeholder={isExamMode ? "Search supervisors..." : "Search and select teacher..."}
                    value={teacherSearch}
                    onChange={(e) => setTeacherSearch(e.target.value)}
                    disabled={isExamMode}
                    className={`w-full px-3 py-2 mb-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 dark:bg-gray-700 dark:text-white text-sm ${
                      isExamMode ? 'bg-gray-100 dark:bg-gray-800 text-gray-500 dark:text-gray-400 cursor-not-allowed' : ''
                    }`}
                  />
                  {!isExamMode && teacherSearch && filteredTeachers.length > 0 && (
                    <div className="absolute z-10 w-full mt-1 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md shadow-lg max-h-40 overflow-y-auto">
                      {filteredTeachers.map((teacher) => (
                        <button
                          key={teacher._id}
                          type="button"
                          onClick={() => {
                            handleInputChange("teacher_id", teacher._id);
                            setTeacherSearch(getTeacherDisplay(teacher));
                          }}
                          className="w-full px-3 py-2 text-left hover:bg-gray-100 dark:hover:bg-gray-600 focus:bg-gray-100 dark:focus:bg-gray-600 focus:outline-none text-sm"
                        >
                          {getTeacherDisplay(teacher)}
                        </button>
                      ))}
                    </div>
                  )}
                </div>
                <select
                  value={formData.teacher_id}
                  onChange={(e) => {
                    handleInputChange("teacher_id", e.target.value);
                    if (!isExamMode) {
                      const selectedTeacher = teachers.find(t => t._id === e.target.value);
                      if (selectedTeacher) {
                        setTeacherSearch(getTeacherDisplay(selectedTeacher));
                      } else {
                        setTeacherSearch("");
                      }
                    }
                  }}
                  disabled={isExamMode}
                  className={`w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 dark:bg-gray-700 dark:text-white ${
                    isExamMode ? 'bg-gray-100 dark:bg-gray-800 text-gray-500 dark:text-gray-400 cursor-not-allowed' : ''
                  }`}
                >
                  <option value="">{isExamMode ? 'Supervisor assignment disabled in exam mode' : 'Select teacher (optional)'}</option>
                  {!isExamMode && teachers.map((teacher) => (
                    <option key={teacher._id} value={teacher._id}>
                      {getTeacherDisplay(teacher)}
                    </option>
                  ))}
                </select>
              </div>

              {/* Day and Period */}
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Day of Week
                  </label>
                  <select
                    value={formData.day_of_week}
                    onChange={(e) => handleInputChange("day_of_week", e.target.value)}
                    className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 dark:bg-gray-700 dark:text-white ${
                      errors.day_of_week 
                        ? "border-red-500 dark:border-red-500" 
                        : "border-gray-300 dark:border-gray-600"
                    }`}
                  >
                    {days.map((day) => (
                      <option key={day} value={day}>
                        {day}
                      </option>
                    ))}
                  </select>
                  {errors.day_of_week && (
                    <p className="mt-1 text-sm text-red-500">{errors.day_of_week}</p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Period
                  </label>
                  <select
                    value={formData.period_id}
                    onChange={(e) => handleInputChange("period_id", e.target.value)}
                    className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 dark:bg-gray-700 dark:text-white ${
                      errors.period_id 
                        ? "border-red-500 dark:border-red-500" 
                        : "border-gray-300 dark:border-gray-600"
                    }`}
                  >
                    <option value="">Select period</option>
                    {periods.map((period) => (
                      <option key={period._id} value={period._id}>
                        {getPeriodDisplay(period)}
                      </option>
                    ))}
                  </select>
                  {errors.period_id && (
                    <p className="mt-1 text-sm text-red-500">{errors.period_id}</p>
                  )}
                </div>
              </div>

              {/* Schedule Type */}
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Schedule Type
                </label>
                <select
                  value={formData.schedule_type}
                  onChange={(e) => handleInputChange("schedule_type", e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 dark:bg-gray-700 dark:text-white"
                >
                  <option value="Normal">Normal</option>
                  <option value="Exam">Exam</option>
                  <option value="Special">Special</option>
                </select>
              </div>

              {/* Actions */}
              <div className="flex justify-end space-x-3 pt-4">
                <button
                  type="button"
                  onClick={onClose}
                  className="px-4 py-2 text-gray-700 dark:text-gray-300 border border-gray-300 dark:border-gray-600 rounded-md hover:bg-gray-50 dark:hover:bg-gray-700"
                  disabled={isSubmitting}
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  disabled={isSubmitting}
                  className="px-4 py-2 bg-indigo-500 text-white rounded-md hover:bg-indigo-600 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2"
                >
                  {isSubmitting ? (
                    <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                  ) : (
                    <Save className="h-4 w-4" />
                  )}
                  <span>{isSubmitting ? "Saving..." : (isEditing ? "Update" : "Create")}</span>
                </button>
              </div>
            </form>
          </motion.div>
        </div>
      )}
    </AnimatePresence>
  );
}
