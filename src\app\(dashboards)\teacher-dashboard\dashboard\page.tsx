"use client";

import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { LayoutDashboard, School, Users, BookOpen, Calendar, BarChart3, Clock } from "lucide-react";
import TeacherLayout from "@/components/Dashboard/Layouts/TeacherLayout";
import useAuth from "@/app/hooks/useAuth";
import ProtectedRoute from "@/components/utils/ProtectedRoute";
import CircularLoader from "@/components/widgets/CircularLoader";
import TeacherDashboardSkeleton from "@/components/skeletons/TeacherDashboardSkeleton";
import { motion } from "framer-motion";
import { getTeacherPermissions, TeacherPermissions } from "@/app/services/TeacherPermissionServices";
import ExamSupervisions from "@/components/Dashboard/Teacher/ExamSupervisions";

interface SelectedSchool {
  school_id: string;
  school_name: string;
  access_granted_at: string;
}

const navigation = {
  icon: LayoutDashboard,
  baseHref: "/teacher-dashboard/dashboard",
  title: "Teacher Dashboard"
};

export default function TeacherDashboardMainPage() {
  const { user, logout } = useAuth();
  const router = useRouter();
  
  const [selectedSchool, setSelectedSchool] = useState<SelectedSchool | null>(null);
  const [loading, setLoading] = useState(true);
  const [permissions, setPermissions] = useState<TeacherPermissions | null>(null);
  const [dashboardData, setDashboardData] = useState({
    studentCount: 0,
    classCount: 0,
    todayScheduleCount: 0,
    pendingGrades: 0
  });

  useEffect(() => {
    // Check if user is a teacher
    if (user && user.role !== "teacher") {
      router.push("/dashboard");
      return;
    }

    // Get selected school from localStorage
    const storedSchool = localStorage.getItem("teacher_selected_school");
    if (storedSchool) {
      try {
        const school = JSON.parse(storedSchool);
        setSelectedSchool(school);
        // Load dashboard data and permissions
        loadDashboardData(school.school_id);
        loadTeacherPermissions(school.school_id);
      } catch (error) {
        console.error("Error parsing stored school:", error);
        router.push("/teacher-dashboard");
      }
    } else {
      // No school selected, redirect to school selection
      router.push("/teacher-dashboard");
    }

    setLoading(false);
  }, [user, router]);

  const loadDashboardData = async (schoolId: string) => {
    try {
      const { getTeacherPermissions, getTeacherStudents } = await import("@/app/services/TeacherPermissionServices");
      const [teacherData, studentsData] = await Promise.all([
        getTeacherPermissions(schoolId),
        getTeacherStudents(schoolId)
      ]);

      setDashboardData({
        studentCount: studentsData.length,
        classCount: teacherData.assigned_classes.length,
        todayScheduleCount: 0, // TODO: Calculate from actual schedule
        pendingGrades: 0 // TODO: Calculate from actual grades
      });
    } catch (error) {
      console.error("Error loading dashboard data:", error);
      setDashboardData({
        studentCount: 0,
        classCount: 0,
        todayScheduleCount: 0,
        pendingGrades: 0
      });
    }
  };

  const loadTeacherPermissions = async (schoolId: string) => {
    try {
      const teacherData = await getTeacherPermissions(schoolId);
      setPermissions(teacherData.permissions);
    } catch (error) {
      console.error("Error loading teacher permissions:", error);
      setPermissions(null);
    }
  };

  const handleSchoolChange = () => {
    localStorage.removeItem("teacher_selected_school");
    router.push("/teacher-dashboard");
  };

  // Check if user has specific permission
  const hasPermission = (module: string, permission: string): boolean => {
    if (!permissions) return false;
    return permissions[module] && (permissions[module] as any)[permission] === true;
  };

  const dashboardStats = [
    {
      title: "My Students",
      value: dashboardData.studentCount.toString(),
      icon: Users,
      color: "bg-blue-500",
      href: "/teacher-dashboard/students"
    },
    {
      title: "My Classes",
      value: dashboardData.classCount.toString(),
      icon: BookOpen,
      color: "bg-green-500",
      href: "/teacher-dashboard/classes"
    },
    {
      title: "Today's Schedule",
      value: dashboardData.todayScheduleCount.toString(),
      icon: Calendar,
      color: "bg-purple-500",
      href: "/teacher-dashboard/timetable"
    },
    {
      title: "Pending Grades",
      value: dashboardData.pendingGrades.toString(),
      icon: BarChart3,
      color: "bg-orange-500",
      href: "/teacher-dashboard/grades"
    }
  ];

  // Define all possible quick actions with their permission requirements
  const allQuickActions = [
    {
      title: "Take Attendance",
      description: "Mark attendance for your classes",
      icon: Clock,
      href: "/teacher-dashboard/attendance",
      color: "bg-teal",
      module: "academic_records",
      permission: "take_attendance_assigned_classes"
    },
    {
      title: "Enter Grades",
      description: "Add or update student grades",
      icon: BarChart3,
      href: "/teacher-dashboard/grades",
      color: "bg-blue-500",
      module: "academic_records",
      permission: "view_grades_assigned_classes"
    },
    {
      title: "View Schedule",
      description: "Check your teaching schedule",
      icon: Calendar,
      href: "/teacher-dashboard/timetable",
      color: "bg-purple-500",
      module: "classes",
      permission: "manage_class_schedules"
    },
    {
      title: "Manage Resources",
      description: "Upload and organize teaching materials",
      icon: BookOpen,
      href: "/teacher-dashboard/resources",
      color: "bg-green-500",
      module: "resources",
      permission: "view_resources"
    }
  ];

  // Filter quick actions based on permissions
  const quickActions = allQuickActions.filter(action =>
    hasPermission(action.module, action.permission)
  );

  if (loading) {
    return (
      <ProtectedRoute allowedRoles={["teacher"]}>
        <TeacherLayout
          navigation={navigation}
          selectedSchool={selectedSchool ? {
            _id: selectedSchool.school_id,
            name: selectedSchool.school_name
          } : null}
          onSchoolChange={handleSchoolChange}
          onLogout={logout}
        >
          <TeacherDashboardSkeleton />
        </TeacherLayout>
      </ProtectedRoute>
    );
  }

  return (
    <ProtectedRoute allowedRoles={["teacher"]}>
      <TeacherLayout
        navigation={navigation}
        selectedSchool={selectedSchool ? {
          _id: selectedSchool.school_id,
          name: selectedSchool.school_name
        } : null}
        onSchoolChange={handleSchoolChange}
        onLogout={logout}
      >
        <div className="space-y-6">
          {/* Welcome Section */}
          <div className="bg-widget rounded-lg border border-stroke p-6">
            <div className="flex items-center space-x-3 mb-4">
              <div className="w-12 h-12 bg-teal rounded-full flex items-center justify-center">
                <School className="h-6 w-6 text-white" />
              </div>
              <div>
                <h1 className="text-2xl font-bold text-foreground">
                  Welcome back, {user?.first_name || user?.name}!
                </h1>
                <p className="text-foreground/60">
                  {selectedSchool?.school_name} • Teacher Dashboard
                </p>
              </div>
            </div>
            
            <div className="bg-teal/10 border border-teal/20 rounded-lg p-4">
              <p className="text-sm text-foreground/70">
                <strong>Current School:</strong> {selectedSchool?.school_name}
              </p>
              <p className="text-sm text-foreground/70">
                <strong>Access Granted:</strong> {selectedSchool ? new Date(selectedSchool.access_granted_at).toLocaleDateString() : 'N/A'}
              </p>
            </div>
          </div>

          {/* Dashboard Stats */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {dashboardStats.map((stat, index) => (
              <motion.div
                key={stat.title}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
                whileHover={{ scale: 1.02 }}
                onClick={() => router.push(stat.href)}
                className="bg-widget rounded-lg border border-stroke p-6 cursor-pointer hover:shadow-lg transition-all"
              >
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-foreground/60">{stat.title}</p>
                    <p className="text-2xl font-bold text-foreground">{stat.value}</p>
                  </div>
                  <div className={`w-12 h-12 ${stat.color} rounded-lg flex items-center justify-center`}>
                    <stat.icon className="h-6 w-6 text-white" />
                  </div>
                </div>
              </motion.div>
            ))}
          </div>

          {/* Quick Actions */}
          <div className="bg-widget rounded-lg border border-stroke p-6">
            <h2 className="text-lg font-semibold text-foreground mb-4">Quick Actions</h2>

            {!permissions ? (
              // Loading permissions
              <div className="text-center py-8">
                <CircularLoader size={24} color="teal" />
                <p className="text-foreground/60 mt-2">Loading available actions...</p>
              </div>
            ) : quickActions.length > 0 ? (
              // Show available quick actions
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {quickActions.map((action, index) => (
                  <motion.div
                    key={action.title}
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: index * 0.1 }}
                    whileHover={{ scale: 1.02 }}
                    onClick={() => router.push(action.href)}
                    className="flex items-center space-x-4 p-4 border border-stroke rounded-lg cursor-pointer hover:border-teal/50 hover:bg-teal/5 transition-all"
                  >
                    <div className={`w-10 h-10 ${action.color} rounded-lg flex items-center justify-center`}>
                      <action.icon className="h-5 w-5 text-white" />
                    </div>
                    <div>
                      <h3 className="font-medium text-foreground">{action.title}</h3>
                      <p className="text-sm text-foreground/60">{action.description}</p>
                    </div>
                  </motion.div>
                ))}
              </div>
            ) : (
              // No permissions available
              <div className="text-center py-8">
                <Clock className="h-12 w-12 text-foreground/30 mx-auto mb-4" />
                <p className="text-foreground/60">No quick actions available</p>
                <p className="text-sm text-foreground/50">
                  Contact your school administrator to get access to teaching features
                </p>
              </div>
            )}
          </div>

          {/* Recent Activity */}
          <div className="bg-widget rounded-lg border border-stroke p-6">
            <h2 className="text-lg font-semibold text-foreground mb-4">Recent Activity</h2>
            
            <div className="text-center py-8">
              <Calendar className="h-12 w-12 text-foreground/30 mx-auto mb-4" />
              <p className="text-foreground/60">No recent activity</p>
              <p className="text-sm text-foreground/50">
                Your recent teaching activities will appear here
              </p>
            </div>
          </div>

          {/* Today's Schedule Preview */}
          <div className="bg-widget rounded-lg border border-stroke p-6">
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-lg font-semibold text-foreground">Today's Schedule</h2>
              <button
                onClick={() => router.push("/teacher-dashboard/timetable")}
                className="text-sm text-teal hover:text-teal-600 font-medium"
              >
                View Full Schedule
              </button>
            </div>

            <div className="text-center py-8">
              <Clock className="h-12 w-12 text-foreground/30 mx-auto mb-4" />
              <p className="text-foreground/60">No classes scheduled for today</p>
              <p className="text-sm text-foreground/50">
                Your daily schedule will appear here
              </p>
            </div>
          </div>

          {/* Exam Supervisions */}
          {selectedSchool && user && (
            <ExamSupervisions
              schoolId={selectedSchool.school_id}
              teacherId={user._id}
              className="col-span-1 lg:col-span-2"
            />
          )}
        </div>
      </TeacherLayout>
    </ProtectedRoute>
  );
}
