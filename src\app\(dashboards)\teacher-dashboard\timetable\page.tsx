"use client";

import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { Calendar, Clock, BookOpen, Users, MapPin } from "lucide-react";
import TeacherLayout from "@/components/Dashboard/Layouts/TeacherLayout";
import useAuth from "@/app/hooks/useAuth";
import ProtectedRoute from "@/components/utils/ProtectedRoute";
import CircularLoader from "@/components/widgets/CircularLoader";
import TeacherTimetableSkeleton from "@/components/skeletons/TeacherTimetableSkeleton";
import { motion } from "framer-motion";
import { getTeacherTimetable, TimetableData, Period, TeacherStats } from "@/app/services/TimetableServices";

interface SelectedSchool {
  school_id: string;
  school_name: string;
  access_granted_at: string;
}



const navigation = {
  icon: Calendar,
  baseHref: "/teacher-dashboard/timetable",
  title: "My Schedule"
};

const DAYS = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday'];

export default function TeacherTimetablePage() {
  const { user, logout } = useAuth();
  const router = useRouter();

  const [selectedSchool, setSelectedSchool] = useState<SelectedSchool | null>(null);
  const [timetableData, setTimetableData] = useState<TimetableData>({});
  const [periods, setPeriods] = useState<Period[]>([]);
  const [teacherStats, setTeacherStats] = useState<TeacherStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    // Check if user is a teacher
    if (user && user.role !== "teacher") {
      router.push("/dashboard");
      return;
    }

    // Get selected school from localStorage
    const storedSchool = localStorage.getItem("teacher_selected_school");
    if (storedSchool) {
      try {
        const school = JSON.parse(storedSchool);
        setSelectedSchool(school);
        fetchTeacherSchedule(school.school_id);
      } catch (error) {
        console.error("Error parsing stored school:", error);
        router.push("/teacher-dashboard");
      }
    } else {
      router.push("/teacher-dashboard");
    }
    
    setLoading(false);
  }, [user, router]);

  const fetchTeacherSchedule = async (schoolId: string) => {
    if (!user?._id) {
      setError("User ID not found");
      return;
    }

    try {
      setError(null);
      const response = await getTeacherTimetable(schoolId, user._id);

      setTimetableData(response.timetable);
      setPeriods(response.periods);
      setTeacherStats(response.teacher_stats);
    } catch (error) {
      console.error("Error fetching teacher schedule:", error);
      setError("Failed to load schedule");
    }
  };

  const handleSchoolChange = () => {
    localStorage.removeItem("teacher_selected_school");
    router.push("/teacher-dashboard");
  };

  const getSubjectColor = (subject: string) => {
    const colors = {
      Mathematics: 'bg-blue-100 text-blue-800 border-blue-200 dark:bg-blue-900/30 dark:text-blue-300',
      'Advanced Mathematics': 'bg-indigo-100 text-indigo-800 border-indigo-200 dark:bg-indigo-900/30 dark:text-indigo-300',
      Physics: 'bg-purple-100 text-purple-800 border-purple-200 dark:bg-purple-900/30 dark:text-purple-300',
      Chemistry: 'bg-green-100 text-green-800 border-green-200 dark:bg-green-900/30 dark:text-green-300',
      Biology: 'bg-orange-100 text-orange-800 border-orange-200 dark:bg-orange-900/30 dark:text-orange-300',
      English: 'bg-red-100 text-red-800 border-red-200 dark:bg-red-900/30 dark:text-red-300'
    };
    return colors[subject as keyof typeof colors] || 'bg-gray-100 text-gray-800 border-gray-200 dark:bg-gray-900/30 dark:text-gray-300';
  };

  const handleClassClick = (scheduleEntry: ScheduleEntry) => {
    // Navigate to attendance or class management
    router.push(`/teacher-dashboard/attendance?class=${scheduleEntry._id}`);
  };

  if (loading) {
    return (
      <ProtectedRoute allowedRoles={["teacher"]}>
        <TeacherLayout
          navigation={navigation}
          selectedSchool={selectedSchool ? {
            _id: selectedSchool.school_id,
            name: selectedSchool.school_name
          } : null}
          onSchoolChange={handleSchoolChange}
          onLogout={logout}
        >
          <TeacherTimetableSkeleton />
        </TeacherLayout>
      </ProtectedRoute>
    );
  }

  return (
    <ProtectedRoute allowedRoles={["teacher"]}>
      <TeacherLayout
        navigation={navigation}
        selectedSchool={selectedSchool ? {
          _id: selectedSchool.school_id,
          name: selectedSchool.school_name
        } : null}
        onSchoolChange={handleSchoolChange}
        onLogout={logout}
      >
        <div className="space-y-6">
          {/* Header */}
          <div className="bg-widget rounded-lg border border-stroke p-6">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <div className="w-12 h-12 bg-teal rounded-full flex items-center justify-center">
                  <Calendar className="h-6 w-6 text-white" />
                </div>
                <div>
                  <h1 className="text-2xl font-bold text-foreground">My Teaching Schedule</h1>
                  <p className="text-foreground/60">
                    Weekly timetable for {selectedSchool?.school_name}
                  </p>
                </div>
              </div>
              
              <div className="text-right">
                <p className="text-2xl font-bold text-foreground">
                  {Object.values(timetableData).reduce((total, day) => 
                    total + Object.values(day).filter(entry => entry !== null).length, 0
                  )}
                </p>
                <p className="text-sm text-foreground/60">Classes This Week</p>
              </div>
            </div>
          </div>

          {/* Quick Stats */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            <div className="bg-widget rounded-lg border border-stroke p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-foreground/60">Total Classes</p>
                  <p className="text-2xl font-bold text-foreground">
                    {Object.values(timetableData).reduce((total, day) => 
                      total + Object.values(day).filter(entry => entry !== null).length, 0
                    )}
                  </p>
                </div>
                <div className="w-10 h-10 bg-blue-500 rounded-lg flex items-center justify-center">
                  <BookOpen className="h-5 w-5 text-white" />
                </div>
              </div>
            </div>

            <div className="bg-widget rounded-lg border border-stroke p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-foreground/60">Different Classes</p>
                  <p className="text-2xl font-bold text-foreground">
                    {teacherStats?.different_classes || 0}
                  </p>
                </div>
                <div className="w-10 h-10 bg-green-500 rounded-lg flex items-center justify-center">
                  <Users className="h-5 w-5 text-white" />
                </div>
              </div>
            </div>

            <div className="bg-widget rounded-lg border border-stroke p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-foreground/60">Total Students</p>
                  <p className="text-2xl font-bold text-foreground">
                    {teacherStats?.total_students || 0}
                  </p>
                </div>
                <div className="w-10 h-10 bg-purple-500 rounded-lg flex items-center justify-center">
                  <Users className="h-5 w-5 text-white" />
                </div>
              </div>
            </div>

            <div className="bg-widget rounded-lg border border-stroke p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-foreground/60">Free Periods</p>
                  <p className="text-2xl font-bold text-foreground">
                    {DAYS.length * periods.length - Object.values(timetableData).reduce((total, day) =>
                      total + Object.values(day).filter(entry => entry !== null).length, 0
                    )}
                  </p>
                </div>
                <div className="w-10 h-10 bg-orange-500 rounded-lg flex items-center justify-center">
                  <Clock className="h-5 w-5 text-white" />
                </div>
              </div>
            </div>
          </div>

          {/* Timetable Grid */}
          <div className="bg-widget rounded-lg border border-stroke p-6">
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-lg font-semibold text-foreground">Weekly Schedule</h2>
            </div>

            <div className="overflow-x-auto">
              <table className="w-full border-collapse">
                <thead>
                  <tr>
                    <th className="border border-stroke p-3 bg-gray-50 dark:bg-gray-800 text-left font-semibold text-foreground">
                      Period / Day
                    </th>
                    {DAYS.map(day => (
                      <th key={day} className="border border-stroke p-3 bg-gray-50 dark:bg-gray-800 text-center font-semibold text-foreground min-w-[180px]">
                        {day}
                      </th>
                    ))}
                  </tr>
                </thead>
                <tbody>
                  {periods.map(period => (
                    <tr key={period.period_number}>
                      <td className="border border-stroke p-3 bg-gray-50 dark:bg-gray-800 font-medium text-foreground">
                        <div className="text-center">
                          <div className="font-semibold">Period {period.period_number}</div>
                          <div className="text-xs text-foreground/60">
                            {period.start_time.slice(0, 5)} - {period.end_time.slice(0, 5)}
                          </div>
                        </div>
                      </td>
                      {DAYS.map(day => {
                        const scheduleEntry = timetableData[day]?.[period.period_number];
                        return (
                          <td 
                            key={`${day}-${period.number}`} 
                            className="border border-stroke p-2"
                          >
                            {scheduleEntry ? (
                              <motion.div 
                                whileHover={{ scale: 1.02 }}
                                onClick={() => handleClassClick(scheduleEntry)}
                                className={`p-3 rounded-lg border-2 cursor-pointer ${getSubjectColor(scheduleEntry.subject_name)} hover:shadow-md transition-all`}
                              >
                                <div className="font-semibold text-sm mb-1">
                                  {scheduleEntry.subject_name}
                                </div>
                                <div className="text-xs opacity-80 mb-1">
                                  {scheduleEntry.class_name}
                                </div>
                                <div className="flex items-center justify-between text-xs opacity-70">
                                  {scheduleEntry.room && (
                                    <div className="flex items-center space-x-1">
                                      <MapPin className="h-3 w-3" />
                                      <span>{scheduleEntry.room}</span>
                                    </div>
                                  )}
                                  <div className="flex items-center space-x-1">
                                    <Users className="h-3 w-3" />
                                    <span>{scheduleEntry.student_count}</span>
                                  </div>
                                </div>
                              </motion.div>
                            ) : (
                              <div className="p-3 rounded-lg border-2 border-dashed border-gray-300 dark:border-gray-600 text-center text-foreground/40">
                                <div className="text-xs">Free Period</div>
                              </div>
                            )}
                          </td>
                        );
                      })}
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>

          {/* Today's Classes */}
          <div className="bg-widget rounded-lg border border-stroke p-6">
            <h2 className="text-lg font-semibold text-foreground mb-4">Today's Classes</h2>
            
            <div className="text-center py-8">
              <Calendar className="h-12 w-12 text-foreground/30 mx-auto mb-4" />
              <p className="text-foreground/60">No classes scheduled for today</p>
              <p className="text-sm text-foreground/50">
                Your today's schedule will appear here
              </p>
            </div>
          </div>
        </div>
      </TeacherLayout>
    </ProtectedRoute>
  );
}
