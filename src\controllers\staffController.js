const User = require('../models/User');
const StaffPermission = require('../models/StaffPermission');
const School = require('../models/School');
const ActivityLog = require('../models/ActivityLog');
const NotificationService = require('../services/notificationService');
const { ensureUniqueId } = require('../utils/generateId');
const bcrypt = require('bcryptjs');
const crypto = require('crypto');
const nodemailer = require('nodemailer');
const admin = require('firebase-admin');
const { getEmailTemplate } = require('../templates/emailTemplates');
const mongoose = require("mongoose");

// Test staff response
const testStaffResponse = async (req, res) => {
  res.json({ message: 'Staff controller is working!' });
};

// Fix missing staff permissions for a school
const fixMissingStaffPermissions = async (req, res) => {
  try {
    const { school_id } = req.params;

    if (!school_id) {
      return res.status(400).json({ message: 'School ID is required' });
    }

    // Find all staff members for this school
    const staffMembers = await User.find({
      school_ids: school_id,
      role: { $in: ['school_admin', 'teacher', 'bursar', 'dean_of_studies'] }
    });

    let fixedCount = 0;
    const results = [];

    for (const staff of staffMembers) {
      // Check if permissions exist
      const existingPermissions = await StaffPermission.findOne({
        user_id: staff._id,
        school_id: school_id
      });

      if (!existingPermissions) {
        try {
          // Generate unique permission ID
          const permissionId = await ensureUniqueId(StaffPermission, 'permission_id', 'PRM');

          // Get default permissions for the user's role
          const defaultPermissions = StaffPermission.getDefaultPermissions(staff.role);

          // Create new StaffPermission
          const newPermissions = new StaffPermission({
            permission_id: permissionId,
            user_id: staff._id,
            school_id: school_id,
            role_template: staff.role,
            permissions: defaultPermissions,
            assigned_classes: [],
            granted_by: req.user?.id || staff._id,
            created_at: new Date(),
            is_active: true
          });

          await newPermissions.save();
          fixedCount++;

          results.push({
            staff_id: staff._id,
            name: `${staff.first_name} ${staff.last_name}`,
            role: staff.role,
            status: 'fixed'
          });
        } catch (error) {
          results.push({
            staff_id: staff._id,
            name: `${staff.first_name} ${staff.last_name}`,
            role: staff.role,
            status: 'error',
            error: error.message
          });
        }
      } else {
        results.push({
          staff_id: staff._id,
          name: `${staff.first_name} ${staff.last_name}`,
          role: staff.role,
          status: 'already_exists'
        });
      }
    }

    res.status(200).json({
      message: `Fixed ${fixedCount} missing staff permissions`,
      total_staff: staffMembers.length,
      fixed_count: fixedCount,
      results: results
    });

  } catch (err) {
    console.error('Error fixing missing staff permissions:', err);
    res.status(500).json({ message: err.message });
  }
};

// Test Firebase integration
const testFirebaseIntegration = async (req, res) => {
  try {
    // Test Firebase Admin SDK connection
    const testUser = await admin.auth().listUsers(1);
    res.json({
      message: 'Firebase integration is working!',
      firebaseConnected: true,
      userCount: testUser.users.length
    });
  } catch (error) {
    console.error('Firebase integration test failed:', error);
    res.status(500).json({
      message: 'Firebase integration failed',
      firebaseConnected: false,
      error: error.message
    });
  }
};

// Sync existing staff with Firebase (utility function)
const syncStaffWithFirebase = async (req, res) => {
  try {
    const { school_id } = req.params;

    if (!school_id) {
      return res.status(400).json({ message: 'School ID is required' });
    }

    // Find all staff members for this school without Firebase UID
    const staffMembers = await User.find({
      school_ids: school_id,
      role: { $in: ['school_admin', 'teacher', 'bursar', 'dean_of_studies'] },
      firebaseUid: { $exists: false }
    });

    const syncResults = [];

    for (const staff of staffMembers) {
      try {
        // Generate a temporary password for Firebase
        const tempPassword = crypto.randomBytes(12).toString('hex');

        // Create Firebase user
        const firebaseUid = await createFirebaseUser(
          staff.email,
          tempPassword,
          staff.name || `${staff.first_name} ${staff.last_name}`
        );

        if (firebaseUid) {
          // Update MongoDB user with Firebase UID
          staff.firebaseUid = firebaseUid;
          await staff.save();

          syncResults.push({
            userId: staff._id,
            email: staff.email,
            firebaseUid: firebaseUid,
            status: 'synced'
          });
        } else {
          syncResults.push({
            userId: staff._id,
            email: staff.email,
            status: 'failed'
          });
        }
      } catch (error) {
        console.error(`Error syncing staff ${staff.email}:`, error);
        syncResults.push({
          userId: staff._id,
          email: staff.email,
          status: 'error',
          error: error.message
        });
      }
    }

    res.status(200).json({
      message: 'Staff Firebase sync completed',
      totalStaff: staffMembers.length,
      results: syncResults
    });

  } catch (err) {
    res.status(500).json({ message: err.message });
  }
};

// Get staff member by ID
const getStaffById = async (req, res) => {
  try {
    const { staff_id } = req.params;

    if (!staff_id) {
      return res.status(400).json({ message: 'Staff ID is required' });
    }

    // Find staff member by staff_id or _id
    const staff = await User.findOne({
      $or: [
        { staff_id: staff_id },
        { _id: staff_id }
      ]
    })
    .populate('school_ids', 'name')
    .populate('access_codes.school_id', 'name')
    .select('-password -temp_password -password_reset_token');

    if (!staff) {
      return res.status(404).json({ message: 'Staff member not found' });
    }

    // Get permissions for this staff member
    const permissions = await StaffPermission.findOne({
      user_id: staff._id
    })
    .populate('school_id', 'name')
    .populate('granted_by', 'first_name last_name');

    const staffWithPermissions = {
      ...staff.toObject(),
      permissions: permissions || null
    };

    res.status(200).json(staffWithPermissions);
  } catch (err) {
    res.status(500).json({ message: err.message });
  }
};

// Get all staff for a specific school
const getStaffBySchool = async (req, res) => {
  try {
    const { school_id } = req.params;

    if (!school_id) {
      return res.status(400).json({ message: 'School ID is required' });
    }

    // Find all staff members for this school
    const staffMembers = await User.find({
      school_ids: school_id,
      role: { $in: ['school_admin', 'teacher', 'bursar', 'dean_of_studies'] }
    })
    .populate('school_ids', 'name')
    .select('-password -temp_password -password_reset_token')
    .sort({ createdAt: -1 });

    // Get permissions for each staff member
    const staffWithPermissions = await Promise.all(
      staffMembers.map(async (staff) => {
        let permissions = await StaffPermission.findOne({
          user_id: staff._id,
          school_id: school_id
        });

        // If no permissions found, create default permissions based on user role
        if (!permissions) {
          console.log(`⚠️  Creating missing permissions for staff: ${staff.first_name} ${staff.last_name} (${staff.role})`);

          try {
            // Generate unique permission ID
            const permissionId = await ensureUniqueId(StaffPermission, 'permission_id', 'PRM');

            // Get default permissions for the user's role
            const defaultPermissions = StaffPermission.getDefaultPermissions(staff.role);

            // Create new StaffPermission
            permissions = new StaffPermission({
              permission_id: permissionId,
              user_id: staff._id,
              school_id: school_id,
              role_template: staff.role,
              permissions: defaultPermissions,
              assigned_classes: [],
              granted_by: req.user?.id || staff._id, // Use current user or self if no user context
              created_at: new Date(),
              is_active: true
            });

            await permissions.save();
            console.log(`✅ Created permissions for ${staff.first_name} ${staff.last_name}`);
          } catch (error) {
            console.error(`❌ Failed to create permissions for ${staff.first_name} ${staff.last_name}:`, error);
            // Continue with null permissions rather than failing the entire request
            permissions = null;
          }
        }

        return {
          ...staff.toObject(),
          permissions: permissions
        };
      })
    );

    // Filter out staff without permissions (in case creation failed)
    const validStaff = staffWithPermissions.filter(staff => staff.permissions !== null);

    res.status(200).json(validStaff);
  } catch (err) {
    res.status(500).json({ message: err.message });
  }
};

// Search for existing teachers
const searchTeachers = async (req, res) => {
  try {
    const { query } = req.query;

    if (!query || query.length < 2) {
      return res.status(400).json({ message: 'Search query must be at least 2 characters' });
    }

    // Search teachers by name, email, or phone
    const teachers = await User.find({
      role: 'teacher',
      $or: [
        { first_name: { $regex: query, $options: 'i' } },
        { last_name: { $regex: query, $options: 'i' } },
        { name: { $regex: query, $options: 'i' } },
        { email: { $regex: query, $options: 'i' } },
        { phone: { $regex: query, $options: 'i' } }
      ]
    })
    .select('-password -temp_password -password_reset_token')
    .limit(10);

    res.status(200).json(teachers);
  } catch (err) {
    res.status(500).json({ message: err.message });
  }
};

// Create new staff member
const createStaff = async (req, res) => {
  try {
    const {
      first_name,
      last_name,
      email,
      phone,
      role_template,
      school_id,
      permissions,
      assigned_classes,
      is_existing_teacher,
      teacher_id
    } = req.body;

    // Validate required fields
    if (!school_id || !role_template) {
      return res.status(400).json({ message: 'School ID and role template are required' });
    }

    let user;
    let isNewUser = false;

    if (is_existing_teacher && teacher_id) {
      // Use existing teacher
      user = await User.findById(teacher_id);
      if (!user || user.role !== 'teacher') {
        return res.status(404).json({ message: 'Teacher not found' });
      }

      // Generate access code for this school
      const accessCode = crypto.randomBytes(8).toString('hex').toUpperCase();
      
      // Add school access if not already present
      const existingAccess = user.access_codes.find(ac => ac.school_id.toString() === school_id);
      if (!existingAccess) {
        user.access_codes.push({
          school_id,
          access_code: accessCode,
          granted_by: req.user.id
        });
        
        // Add school to school_ids if not present
        if (!user.school_ids.includes(school_id)) {
          user.school_ids.push(school_id);
        }
        
        await user.save();
      }
    } else {
      // Create new staff member
      if (!first_name || !last_name || !email) {
        return res.status(400).json({ message: 'First name, last name, and email are required for new staff' });
      }

      // Check if user already exists
      const existingUser = await User.findOne({ email });
      if (existingUser) {
        return res.status(400).json({ message: 'User with this email already exists' });
      }

      // Generate unique IDs
      const userId = await ensureUniqueId(User, 'user_id', 'USR');
      const staffId = await ensureUniqueId(User, 'staff_id', 'STF');
      
      // Generate temporary password
      const tempPassword = crypto.randomBytes(12).toString('hex');
      const hashedTempPassword = await bcrypt.hash(tempPassword, 10);
      
      // Generate password reset token
      const resetToken = crypto.randomBytes(32).toString('hex');
      const resetTokenExpiry = new Date(Date.now() + 24 * 60 * 60 * 1000); // 24 hours

      // Create Firebase user first
      const firebaseUid = await createFirebaseUser(
        email,
        tempPassword,
        `${first_name} ${last_name}`
      );

      user = new User({
        user_id: userId,
        staff_id: staffId,
        firebaseUid: firebaseUid, // Store Firebase UID if created successfully
        first_name,
        last_name,
        name: `${first_name} ${last_name}`,
        email,
        phone,
        password: hashedTempPassword,
        role: role_template === 'teacher' ? 'teacher' : role_template,
        school_ids: [school_id],
        temp_password: hashedTempPassword,
        temp_password_expires: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7 days
        password_reset_token: resetToken,
        password_reset_expires: resetTokenExpiry,
        isVerified: false
      });

      // For teachers, add access code
      if (role_template === 'teacher') {
        const accessCode = crypto.randomBytes(8).toString('hex').toUpperCase();
        user.access_codes = [{
          school_id,
          access_code: accessCode,
          granted_by: req.user.id
        }];
      }

      await user.save();
      isNewUser = true;

      // Get school name for email
      const school = await School.findById(school_id);
      const schoolName = school ? school.name : 'Your School';

      // Get access code for teachers
      const userAccessCode = role_template === 'teacher' && user.access_codes.length > 0
        ? user.access_codes[0].access_code
        : null;

      // Send welcome email
      console.log('Attempting to send welcome email to:', user.email);
      console.log('Email config:', {
        EMAIL_USER: process.env.EMAIL_USER,
        EMAIL_PASS: process.env.EMAIL_PASS ? '***' : 'NOT_SET',
        FRONTEND_URL: process.env.FRONTEND_URL
      });
      await sendPasswordResetEmail(user, resetToken, role_template, true, schoolName, userAccessCode);

      // Log activity for new user creation
      await ActivityLog.logActivity({
        user_id: req.user.id,
        school_id: school_id,
        action: 'staff_created',
        target_type: 'user',
        target_id: user.staff_id || user._id,
        target_name: `${user.first_name} ${user.last_name}`,
        details: {
          role: role_template,
          email: user.email,
          is_new_user: true
        },
        ip_address: req.ip,
        user_agent: req.get('User-Agent')
      });
    }

    // Create staff permissions
    const permissionId = await ensureUniqueId(StaffPermission, 'permission_id', 'PRM');
    
    // Get default permissions for role template
    const defaultPermissions = StaffPermission.getDefaultPermissions(role_template);
    
    // Merge with custom permissions if provided
    const finalPermissions = permissions ? { ...defaultPermissions, ...permissions } : defaultPermissions;

    const staffPermission = new StaffPermission({
      permission_id: permissionId,
      user_id: user._id,
      school_id,
      role_template,
      permissions: finalPermissions,
      assigned_classes: assigned_classes || [],
      granted_by: req.user.id
    });

    await staffPermission.save();

    // Populate the response
    const populatedUser = await User.findById(user._id)
      .populate('school_ids', 'name')
      .select('-password -temp_password -password_reset_token');

    const populatedPermission = await StaffPermission.findById(staffPermission._id)
      .populate('user_id', 'first_name last_name email')
      .populate('school_id', 'name')
      .populate('granted_by', 'first_name last_name');

    // 🔔 SEND NOTIFICATIONS FOR STAFF CREATION
    try {
      await sendStaffCreationNotifications(user, school_id, role_template, isNewUser, req.user.id);
    } catch (notificationError) {
      console.error('Error sending staff creation notifications:', notificationError);
      // Don't fail the request if notifications fail
    }

    res.status(201).json({
      user: populatedUser,
      permissions: populatedPermission,
      isNewUser,
      message: isNewUser ? 'Staff member created and invitation sent' : 'Teacher access granted'
    });

  } catch (err) {
    res.status(400).json({ message: err.message });
  }
};

// Update staff member
const updateStaff = async (req, res) => {
  try {
    const { staff_id } = req.params;
    const {
      first_name,
      last_name,
      email,
      phone,
      role_template,
      permissions,
      assigned_classes,
      is_active,
      school_id
    } = req.body;

    // Find the user
    let user = await User.findOne({ staff_id });
    if (!user) {
      const _id = new mongoose.Types.ObjectId(req.params.staff_id);
      // Find the user
      user = await User.findById(_id);
      if (!user) {
        return res.status(404).json({ message: 'Staff member not found' });
      }
    }

    // Prepare Firebase updates
    const firebaseUpdates = {};
    let shouldUpdateFirebase = false;

    // Update user information
    if (first_name) {
      user.first_name = first_name;
      shouldUpdateFirebase = true;
    }
    if (last_name) {
      user.last_name = last_name;
      shouldUpdateFirebase = true;
    }
    if (first_name || last_name) {
      user.name = `${user.first_name} ${user.last_name}`;
      firebaseUpdates.displayName = user.name;
    }
    if (email) {
      user.email = email;
      firebaseUpdates.email = email;
      shouldUpdateFirebase = true;
    }
    if (phone) user.phone = phone;
    if (typeof is_active !== 'undefined') {
      user.is_staff_active = is_active;
      firebaseUpdates.disabled = !is_active;
      shouldUpdateFirebase = true;
    }

    // Update Firebase user if needed
    if (user.firebaseUid && shouldUpdateFirebase) {
      await updateFirebaseUser(user.firebaseUid, firebaseUpdates);
    }

    await user.save();

    // Update permissions
    const staffPermission = await StaffPermission.findOne({
      user_id: user._id,
      school_id: req.body.school_id
    });

    if (staffPermission) {
      if (role_template) staffPermission.role_template = role_template;
      if (permissions) staffPermission.permissions = permissions;
      if (assigned_classes) staffPermission.assigned_classes = assigned_classes;
      staffPermission.last_modified_by = req.user.id;
      staffPermission.last_modified_at = new Date();

      await staffPermission.save();
    }

    // Populate the response
    const populatedUser = await User.findById(user._id)
      .populate('school_ids', 'name')
      .select('-password -temp_password -password_reset_token');

    const populatedPermission = await StaffPermission.findById(staffPermission._id)
      .populate('user_id', 'first_name last_name email')
      .populate('school_id', 'name')
      .populate('granted_by', 'first_name last_name');

    // Log activity
    await ActivityLog.logActivity({
      user_id: req.user.id,
      school_id: school_id,
      action: 'staff_updated',
      target_type: 'user',
      target_id: user.staff_id || user._id,
      target_name: `${user.first_name} ${user.last_name}`,
      details: {
        updated_fields: Object.keys(req.body).filter(key => key !== 'school_id'),
        role: user.role
      },
      ip_address: req.ip,
      user_agent: req.get('User-Agent')
    });

    res.status(200).json({
      user: populatedUser,
      permissions: populatedPermission,
      message: 'Staff member updated successfully'
    });

  } catch (err) {
    res.status(400).json({ message: err.message });
  }
};

// Delete staff member (remove from school)
const deleteStaff = async (req, res) => {
  try {
    const { staff_id } = req.params;
    const { school_id } = req.body;

    if (!school_id) {
      return res.status(400).json({ message: 'School ID is required' });
    }

    // Find the user
    const user = await User.findOne({ staff_id });
    if (!user) {
      return res.status(404).json({ message: 'Staff member not found' });
    }

    // Remove school from user's school_ids
    user.school_ids = user.school_ids.filter(id => id.toString() !== school_id);

    // Remove access code for this school (for teachers)
    user.access_codes = user.access_codes.filter(ac => ac.school_id.toString() !== school_id);

    // Check if user has no more schools - if so, disable Firebase account
    if (user.school_ids.length === 0 && user.firebaseUid) {
      await updateFirebaseUser(user.firebaseUid, { disabled: true });
    }

    await user.save();

    // Remove staff permissions for this school
    await StaffPermission.findOneAndDelete({
      user_id: user._id,
      school_id: school_id
    });

    // Log activity
    await ActivityLog.logActivity({
      user_id: req.user.id,
      school_id: school_id,
      action: 'staff_deleted',
      target_type: 'user',
      target_id: user.staff_id || user._id,
      target_name: `${user.first_name} ${user.last_name}`,
      details: {
        role: user.role,
        email: user.email,
        remaining_schools: user.school_ids.length
      },
      ip_address: req.ip,
      user_agent: req.get('User-Agent')
    });

    res.status(200).json({ message: 'Staff member removed from school successfully' });

  } catch (err) {
    res.status(500).json({ message: err.message });
  }
};

// Reset staff password
const resetStaffPassword = async (req, res) => {
  try {
    const { staff_id } = req.params;

    // Find the user
    const user = await User.findOne({ staff_id });
    if (!user) {
      return res.status(404).json({ message: 'Staff member not found' });
    }

    // Generate password reset token
    const resetToken = crypto.randomBytes(32).toString('hex');
    const resetTokenExpiry = new Date(Date.now() + 24 * 60 * 60 * 1000); // 24 hours

    // If user has Firebase UID, ensure Firebase user is enabled for password reset
    if (user.firebaseUid) {
      await updateFirebaseUser(user.firebaseUid, { disabled: false });
    }

    user.password_reset_token = resetToken;
    user.password_reset_expires = resetTokenExpiry;
    await user.save();

    // Send password reset email
    await sendPasswordResetEmail(user, resetToken, user.role, false);

    // Log activity
    await ActivityLog.logActivity({
      user_id: req.user.id,
      school_id: user.school_ids?.[0], // Use first school if available
      action: 'staff_password_reset',
      target_type: 'user',
      target_id: user.staff_id || user._id,
      target_name: `${user.first_name} ${user.last_name}`,
      details: {
        role: user.role,
        email: user.email
      },
      ip_address: req.ip,
      user_agent: req.get('User-Agent')
    });

    res.status(200).json({ message: 'Password reset email sent successfully' });

  } catch (err) {
    res.status(500).json({ message: err.message });
  }
};

// Generate new access code for teacher
const generateAccessCode = async (req, res) => {
  try {
    const { teacher_id, school_id } = req.body;

    if (!teacher_id || !school_id) {
      return res.status(400).json({ message: 'Teacher ID and School ID are required' });
    }

    // Find the teacher
    const teacher = await User.findById(teacher_id);
    if (!teacher || teacher.role !== 'teacher') {
      return res.status(404).json({ message: 'Teacher not found' });
    }

    // Generate new access code
    const accessCode = crypto.randomBytes(8).toString('hex').toUpperCase();

    // Update or add access code for this school
    const existingAccessIndex = teacher.access_codes.findIndex(ac => ac.school_id.toString() === school_id);

    if (existingAccessIndex !== -1) {
      teacher.access_codes[existingAccessIndex].access_code = accessCode;
      teacher.access_codes[existingAccessIndex].granted_at = new Date();
      teacher.access_codes[existingAccessIndex].granted_by = req.user.id;
    } else {
      teacher.access_codes.push({
        school_id,
        access_code: accessCode,
        granted_by: req.user.id
      });

      // Add school to school_ids if not present
      if (!teacher.school_ids.includes(school_id)) {
        teacher.school_ids.push(school_id);
      }
    }

    // Ensure Firebase user is enabled when generating new access code
    if (teacher.firebaseUid) {
      await updateFirebaseUser(teacher.firebaseUid, { disabled: false });
    }

    await teacher.save();

    // Send access code email
    try {
      const school = await School.findById(school_id);
      await sendAccessCodeEmail(teacher, accessCode, school ? school.name : 'School');
    } catch (emailError) {
      console.error('Error sending access code email:', emailError);
      // Continue even if email fails
    }

    // Log activity
    await ActivityLog.logActivity({
      user_id: req.user.id,
      school_id: school_id,
      action: 'staff_access_code_generated',
      target_type: 'user',
      target_id: teacher.staff_id || teacher._id,
      target_name: `${teacher.first_name} ${teacher.last_name}`,
      details: {
        role: teacher.role,
        email: teacher.email,
        access_code: accessCode
      },
      ip_address: req.ip,
      user_agent: req.get('User-Agent')
    });

    res.status(200).json({
      access_code: accessCode,
      message: 'Access code generated successfully'
    });

  } catch (err) {
    res.status(500).json({ message: err.message });
  }
};

// Reset password with token (for teachers)
const resetPasswordWithToken = async (req, res) => {
  try {
    const { token, password, userType } = req.body;

    if (!token || !password) {
      return res.status(400).json({ message: 'Token and password are required' });
    }

    // Find user by reset token
    const user = await User.findOne({
      password_reset_token: token,
      password_reset_expires: { $gt: Date.now() }
    });

    if (!user) {
      return res.status(400).json({ message: 'Invalid or expired reset token' });
    }

    // Verify user type if specified
    if (userType === 'teacher' && user.role !== 'teacher') {
      return res.status(400).json({ message: 'Invalid user type for this reset link' });
    }

    // Hash new password
    const hashedPassword = await bcrypt.hash(password, 10);

    // Update Firebase password if user has Firebase UID
    if (user.firebaseUid) {
      await updateFirebaseUser(user.firebaseUid, {
        password: password,
        emailVerified: true,
        disabled: false
      });
    }

    // Update user password and clear reset token
    user.password = hashedPassword;
    user.password_reset_token = undefined;
    user.password_reset_expires = undefined;
    user.temp_password = undefined;
    user.temp_password_expires = undefined;
    user.isVerified = true;

    await user.save();

    res.status(200).json({ message: 'Password reset successfully' });

  } catch (err) {
    res.status(500).json({ message: err.message });
  }
};

// Helper function to create Firebase user safely
const createFirebaseUser = async (email, password, displayName) => {
  try {
    const firebaseUser = await admin.auth().createUser({
      email,
      password,
      displayName,
      emailVerified: false,
      disabled: false
    });
    return firebaseUser.uid;
  } catch (firebaseError) {
    console.error('Error creating Firebase user:', firebaseError);

    // Handle specific Firebase errors
    if (firebaseError.code === 'auth/email-already-exists') {
      // Try to get the existing user
      try {
        const existingUser = await admin.auth().getUserByEmail(email);
        console.log('Firebase user already exists, using existing UID:', existingUser.uid);
        return existingUser.uid;
      } catch (getError) {
        console.error('Error getting existing Firebase user:', getError);
        return null;
      }
    }

    return null;
  }
};

// Helper function to update Firebase user safely
const updateFirebaseUser = async (firebaseUid, updates) => {
  try {
    await admin.auth().updateUser(firebaseUid, updates);
    return true;
  } catch (firebaseError) {
    console.error('Error updating Firebase user:', firebaseError);
    return false;
  }
};

// Send access code email
const sendAccessCodeEmail = async (user, accessCode, schoolName) => {
  try {
    // Configure nodemailer
    const transporter = nodemailer.createTransport({
      service: 'gmail',
      auth: {
        user: process.env.EMAIL_USER,
        pass: process.env.EMAIL_PASS
      }
    });

    const emailData = {
      firstName: user.first_name,
      schoolName: schoolName,
      accessCode: accessCode,
      generatedAt: new Date().toLocaleString(),
      dashboardUrl: `${process.env.FRONTEND_URL}/teacher-dashboard`
    };

    const htmlContent = getEmailTemplate('accessCodeGenerated', emailData);

    const mailOptions = {
      from: `"Scholarify Team" <${process.env.EMAIL_USER}>`,
      to: user.email,
      subject: `New Access Code for ${schoolName} - Scholarify`,
      html: htmlContent
    };

    await transporter.sendMail(mailOptions);
    console.log(`Access code email sent successfully to ${user.email}`);
  } catch (error) {
    console.error('Error sending access code email:', error);
  }
};

// Send password reset email
const sendPasswordResetEmail = async (user, resetToken, roleTemplate, isNewUser = true, schoolName = null, accessCode = null) => {
  try {
    console.log('sendPasswordResetEmail called with:', {
      userEmail: user.email,
      roleTemplate,
      isNewUser,
      schoolName,
      hasAccessCode: !!accessCode
    });

    // Configure nodemailer
    const transporter = nodemailer.createTransport({
      service: 'gmail', // or your email service
      auth: {
        user: process.env.EMAIL_USER,
        pass: process.env.EMAIL_PASS
      }
    });

    console.log('Nodemailer transporter created successfully');
    const frontendUrl = process.env.FRONTEND_URL || 'http://localhost:3000';
    const resetUrl = roleTemplate === 'teacher'
      ? `${frontendUrl}/teacher-dashboard/reset-password?token=${resetToken}`
      : `${frontendUrl}/school-admin/reset-password?token=${resetToken}`;

    // Get role display name
    const roleDisplayNames = {
      school_admin: 'School Administrator',
      teacher: 'Teacher',
      bursar: 'Bursar',
      dean_of_studies: 'Dean of Studies',
      custom: 'Staff Member'
    };

    const roleDisplay = roleDisplayNames[roleTemplate] || roleTemplate;

    // Prepare email data
    const emailData = {
      firstName: user.first_name,
      email: user.email,
      role: roleTemplate,
      roleDisplay: roleDisplay,
      resetUrl: resetUrl,
      schoolName: schoolName || 'Your School',
      accessCode: accessCode,
      expiryTime: new Date(Date.now() + 24 * 60 * 60 * 1000).toLocaleString()
    };

    // Choose template based on context
    const templateType = isNewUser ? 'staffWelcome' : 'passwordReset';
    const htmlContent = getEmailTemplate(templateType, emailData);

    const mailOptions = {
      from: `"Scholarify Team" <${process.env.EMAIL_USER}>`,
      to: user.email,
      subject: isNewUser
        ? `Welcome to Scholarify - ${roleDisplay} Account Created`
        : 'Scholarify - Password Reset Request',
      html: htmlContent
    };

    console.log('Attempting to send email with options:', {
      from: mailOptions.from,
      to: mailOptions.to,
      subject: mailOptions.subject
    });

    const result = await transporter.sendMail(mailOptions);
    console.log(`Email sent successfully to ${user.email}`, result);
  } catch (error) {
    console.error('Error sending email:', error);
    console.error('Error details:', error.message);
    // Don't throw error, just log it
  }
};

// ===== STAFF PERMISSIONS FUNCTIONS =====

// Test route for staff permissions
const testStaffPermissionResponse = (req, res) => {
  res.status(200).json({ message: 'Staff Permission route is working!' });
};

// Get staff permissions for a specific user in a specific school
const getStaffPermissions = async (req, res) => {
  try {
    const { userId, schoolId } = req.params;

    if (!userId || !schoolId) {
      return res.status(400).json({ message: 'User ID and School ID are required' });
    }

    const staffPermission = await StaffPermission.findOne({
      user_id: userId,
      school_id: schoolId,
      is_active: true
    })
    .populate('user_id', 'first_name last_name email role')
    .populate('school_id', 'name')
    .populate('granted_by', 'first_name last_name')
    .populate('last_modified_by', 'first_name last_name')
    .populate('assigned_classes.class_id', 'name');

    if (!staffPermission) {
      return res.status(404).json({ message: 'Staff permissions not found' });
    }

    res.status(200).json(staffPermission);
  } catch (error) {
    console.error('Error fetching staff permissions:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
};

// Get current user's staff permissions for a specific school
const getCurrentUserStaffPermissions = async (req, res) => {
  try {
    const { schoolId } = req.params;
    const userId = req.user._id; // From auth middleware

    if (!schoolId) {
      return res.status(400).json({ message: 'School ID is required' });
    }

    const staffPermission = await StaffPermission.findOne({
      user_id: userId,
      school_id: schoolId,
      is_active: true
    })
    .populate('user_id', 'first_name last_name email role')
    .populate('school_id', 'name')
    .populate('granted_by', 'first_name last_name')
    .populate('last_modified_by', 'first_name last_name')
    .populate('assigned_classes.class_id', 'name');

    if (!staffPermission) {
      return res.status(404).json({ message: 'Staff permissions not found for current user' });
    }

    res.status(200).json(staffPermission);
  } catch (error) {
    console.error('Error fetching current user staff permissions:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
};

// Get all staff permissions for a school
const getSchoolStaffPermissions = async (req, res) => {
  try {
    const { schoolId } = req.params;

    if (!schoolId) {
      return res.status(400).json({ message: 'School ID is required' });
    }

    const staffPermissions = await StaffPermission.find({
      school_id: schoolId,
      is_active: true
    })
    .populate('user_id', 'first_name last_name email role')
    .populate('school_id', 'name')
    .populate('granted_by', 'first_name last_name')
    .populate('last_modified_by', 'first_name last_name')
    .populate('assigned_classes.class_id', 'name')
    .sort({ createdAt: -1 });

    res.status(200).json(staffPermissions);
  } catch (error) {
    console.error('Error fetching school staff permissions:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
};

// Create staff permissions
const createStaffPermissions = async (req, res) => {
  try {
    const { user_id, school_id, role_template, permissions, assigned_classes } = req.body;

    if (!user_id || !school_id || !role_template) {
      return res.status(400).json({
        message: 'User ID, School ID, and role template are required'
      });
    }

    // Check if user exists
    const user = await User.findById(user_id);
    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }

    // Check if permissions already exist
    const existingPermission = await StaffPermission.findOne({
      user_id,
      school_id,
      is_active: true
    });

    if (existingPermission) {
      return res.status(400).json({
        message: 'Staff permissions already exist for this user in this school'
      });
    }

    // Generate unique permission ID
    const permissionId = await ensureUniqueId(StaffPermission, 'permission_id', 'PRM');

    // Get default permissions for role template
    const defaultPermissions = StaffPermission.getDefaultPermissions(role_template);

    // Merge with custom permissions if provided
    const finalPermissions = permissions ? { ...defaultPermissions, ...permissions } : defaultPermissions;

    const staffPermission = new StaffPermission({
      permission_id: permissionId,
      user_id,
      school_id,
      role_template,
      permissions: finalPermissions,
      assigned_classes: assigned_classes || [],
      granted_by: req.user._id
    });

    await staffPermission.save();

    // Populate the response
    const populatedPermission = await StaffPermission.findById(staffPermission._id)
      .populate('user_id', 'first_name last_name email role')
      .populate('school_id', 'name')
      .populate('granted_by', 'first_name last_name')
      .populate('assigned_classes.class_id', 'name');

    res.status(201).json(populatedPermission);
  } catch (error) {
    console.error('Error creating staff permissions:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
};

// Update staff permissions
const updateStaffPermissions = async (req, res) => {
  try {
    const { permissionId } = req.params;
    const { role_template, permissions, assigned_classes, is_active } = req.body;

    if (!permissionId) {
      return res.status(400).json({ message: 'Permission ID is required' });
    }

    const staffPermission = await StaffPermission.findById(permissionId);
    if (!staffPermission) {
      return res.status(404).json({ message: 'Staff permissions not found' });
    }

    // Update fields
    if (role_template) staffPermission.role_template = role_template;
    if (permissions) staffPermission.permissions = permissions;
    if (assigned_classes) staffPermission.assigned_classes = assigned_classes;
    if (typeof is_active !== 'undefined') staffPermission.is_active = is_active;

    staffPermission.last_modified_by = req.user._id;
    staffPermission.last_modified_at = new Date();

    await staffPermission.save();

    // Populate the response
    const populatedPermission = await StaffPermission.findById(staffPermission._id)
      .populate('user_id', 'first_name last_name email role')
      .populate('school_id', 'name')
      .populate('granted_by', 'first_name last_name')
      .populate('last_modified_by', 'first_name last_name')
      .populate('assigned_classes.class_id', 'name');

    res.status(200).json(populatedPermission);
  } catch (error) {
    console.error('Error updating staff permissions:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
};

// Delete staff permissions
const deleteStaffPermissions = async (req, res) => {
  try {
    const { permissionId } = req.params;

    if (!permissionId) {
      return res.status(400).json({ message: 'Permission ID is required' });
    }

    const staffPermission = await StaffPermission.findById(permissionId);
    if (!staffPermission) {
      return res.status(404).json({ message: 'Staff permissions not found' });
    }

    // Soft delete by setting is_active to false
    staffPermission.is_active = false;
    staffPermission.last_modified_by = req.user._id;
    staffPermission.last_modified_at = new Date();

    await staffPermission.save();

    res.status(200).json({ message: 'Staff permissions deleted successfully' });
  } catch (error) {
    console.error('Error deleting staff permissions:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
};

// Get default permissions for a role template
const getDefaultPermissionsForRole = async (req, res) => {
  try {
    const { roleTemplate } = req.params;

    if (!roleTemplate) {
      return res.status(400).json({ message: 'Role template is required' });
    }

    const defaultPermissions = StaffPermission.getDefaultPermissions(roleTemplate);

    if (!defaultPermissions || Object.keys(defaultPermissions).length === 0) {
      return res.status(404).json({ message: 'Default permissions not found for this role template' });
    }

    res.status(200).json(defaultPermissions);
  } catch (error) {
    console.error('Error fetching default permissions:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
};

// 🔔 NOTIFICATION HELPER FUNCTION FOR STAFF CREATION
const sendStaffCreationNotifications = async (newStaff, schoolId, roleTemplate, isNewUser, creatorId) => {
  try {
    const school = await School.findById(schoolId);
    const creator = await User.findById(creatorId);

    if (!school) {
      console.error('School not found for notifications');
      return;
    }

    // 1. Notify the new staff member (welcome notification)
    if (isNewUser) {
      await NotificationService.createNotification({
        recipient_id: newStaff._id,
        school_id: schoolId,
        type: 'success',
        category: 'staff',
        title: 'Welcome to the Team! 🎉',
        message: `Welcome to ${school.name}! Your account has been created as a ${roleTemplate.replace('_', ' ')}. Please check your email for login instructions.`,
        sender_id: creatorId,
        sender_type: 'user',
        action_url: `/login`,
        action_label: 'Login Now',
        priority: 'high',
        channels: {
          in_app: true,
          email: true
        }
      });
    }

    // 2. Notify all school admins about the new staff member
    const schoolAdmins = await User.find({
      school_ids: schoolId,
      role: { $in: ['school_admin', 'admin', 'super'] },
      _id: { $ne: creatorId } // Exclude the creator
    });

    for (const admin of schoolAdmins) {
      await NotificationService.createNotification({
        recipient_id: admin._id,
        school_id: schoolId,
        type: 'info',
        category: 'staff',
        title: 'New Staff Member Added',
        message: `${newStaff.first_name} ${newStaff.last_name} has been added as a ${roleTemplate.replace('_', ' ')} by ${creator ? creator.first_name + ' ' + creator.last_name : 'System'}.`,
        sender_id: creatorId,
        sender_type: 'user',
        related_entity: {
          entity_type: 'staff',
          entity_id: newStaff._id
        },
        action_url: `/school-admin/staff`,
        action_label: 'View Staff',
        channels: {
          in_app: true
        }
      });
    }

    // 3. If it's a teacher, notify other teachers (optional)
    if (roleTemplate === 'teacher') {
      const otherTeachers = await User.find({
        school_ids: schoolId,
        role: 'teacher',
        _id: { $ne: newStaff._id } // Exclude the new teacher
      }).limit(5); // Limit to avoid spam

      for (const teacher of otherTeachers) {
        await NotificationService.createNotification({
          recipient_id: teacher._id,
          school_id: schoolId,
          type: 'info',
          category: 'staff',
          title: 'New Teacher Joined',
          message: `${newStaff.first_name} ${newStaff.last_name} has joined the teaching staff. Welcome them to the team!`,
          sender_type: 'system',
          related_entity: {
            entity_type: 'staff',
            entity_id: newStaff._id
          },
          priority: 'low',
          channels: {
            in_app: true
          }
        });
      }
    }

    // 4. Create a system notification for audit purposes
    await NotificationService.createNotification({
      recipient_id: creatorId,
      school_id: schoolId,
      type: 'success',
      category: 'system',
      title: 'Staff Creation Successful',
      message: `Successfully created ${roleTemplate.replace('_', ' ')} account for ${newStaff.first_name} ${newStaff.last_name}.`,
      sender_type: 'system',
      related_entity: {
        entity_type: 'staff',
        entity_id: newStaff._id
      },
      action_url: `/school-admin/staff`,
      action_label: 'View Staff List',
      channels: {
        in_app: true
      }
    });

    console.log(`✅ Staff creation notifications sent successfully for ${newStaff.first_name} ${newStaff.last_name}`);

  } catch (error) {
    console.error('❌ Error sending staff creation notifications:', error);
    throw error;
  }
};

// Get teachers available for assignment in a school
const getTeachersBySchool = async (req, res) => {
  try {
    const { school_id } = req.params;

    // Find all users with teacher role who have access to this school
    const teachers = await User.find({
      role: 'teacher',
      $or: [
        { school_ids: school_id },
        { 'access_codes.school_id': school_id, 'access_codes.is_active': true }
      ]
    }).select('_id first_name last_name name email');

    // Format response
    const formattedTeachers = teachers.map(teacher => ({
      _id: teacher._id,
      first_name: teacher.first_name,
      last_name: teacher.last_name,
      name: teacher.name,
      email: teacher.email,
      display_name: teacher.first_name && teacher.last_name
        ? `${teacher.first_name} ${teacher.last_name}`
        : teacher.name
    }));

    res.json({
      teachers: formattedTeachers,
      total: formattedTeachers.length
    });

  } catch (error) {
    console.error('Error fetching teachers by school:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
};

const RemoveStaff = async (req, res) => {
  try {
    const { user_id, school_id } = req.body;

    if (!user_id || !school_id) {
      return res.status(400).json({ message: "user_id and school_id are required" });
    }

    const user = await User.findOne({ user_id });

    if (!user) {
      return res.status(404).json({ message: "User not found" });
    }

    if (user.role === "teacher") {
      // Remove the school_id from the school_ids array
      user.school_ids = user.school_ids.filter(
        (id) => id.toString() !== school_id
      );

      // Also remove from access_codes
      user.access_codes = user.access_codes.filter(
        (code) => code.school_id.toString() !== school_id
      );

      if (user.school_ids.length === 0) {
        // No schools left → delete user
        await User.deleteOne({ user_id });
        return res.status(200).json({ message: "Teacher removed entirely" });
      } else {
        // Just update the record
        await user.save();
        return res.status(200).json({ message: "School removed from teacher's account" });
      }
    } else {
      // Delete non-teacher staff
      await User.deleteOne({ user_id });
      return res.status(200).json({ message: "Staff deleted successfully" });
    }
  } catch (error) {
    console.error("Delete Staff Error:", error);
    return res.status(500).json({ message: "Internal Server Error" });
  }
};

module.exports = {
  testStaffResponse,
  testFirebaseIntegration,
  syncStaffWithFirebase,
  fixMissingStaffPermissions,
  getStaffById,
  getStaffBySchool,
  getTeachersBySchool,
  searchTeachers,
  createStaff,
  updateStaff,
  deleteStaff,
  resetStaffPassword,
  generateAccessCode,
  resetPasswordWithToken,
  // Staff permissions functions
  testStaffPermissionResponse,
  getStaffPermissions,
  getCurrentUserStaffPermissions,
  getSchoolStaffPermissions,
  createStaffPermissions,
  updateStaffPermissions,
  deleteStaffPermissions,
  getDefaultPermissionsForRole,
  RemoveStaff
};
