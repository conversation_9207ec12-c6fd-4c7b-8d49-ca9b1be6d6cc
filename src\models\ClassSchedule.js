const mongoose = require('mongoose');

const ClassScheduleSchema = new mongoose.Schema(
  {
    school_id:{
      type: mongoose.Schema.Types.ObjectId,
      ref: "School",
      required: true,
    },
    class_id: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Class",
      required: true,
    },
    subject_id: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Subject",
      required: true,
    },
    schedule_type:{
      type: String,
      required: true,  
      enum: ['Normal', 'Exam', 'Event'],
    },
    period_id: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Period",
      required: true,
    },
    teacher_id: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "User",
    },
    day_of_week: {
      type: String,
      enum: ["Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday", "Sunday"],
      required: true,
    },

    // Exam period reference (only for Exam schedule_type)
    exam_period_id: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "ExamPeriod",
      required: false // Only required when schedule_type is 'Exam'
    },

    // Academic year
    academic_year: {
      type: String,
      required: true
    },

    // Priority (higher number = higher priority)
    priority: {
      type: Number,
      default: function() {
        return this.schedule_type === 'Exam' ? 100 : 50;
      },
      min: 1,
      max: 200
    },

    // Status
    status: {
      type: String,
      enum: ['active', 'suspended', 'cancelled', 'completed'],
      default: 'active'
    },

    // Specific date (for one-time events or specific exam dates)
    specific_date: {
      type: Date,
      required: false
    },

    // Notes
    notes: {
      type: String,
      maxlength: 500
    }
  },
  {
    timestamps: true,
  }
);

// Indexes
ClassScheduleSchema.index({ school_id: 1, class_id: 1, day_of_week: 1, period_id: 1 });
ClassScheduleSchema.index({ school_id: 1, teacher_id: 1, day_of_week: 1, period_id: 1 });
ClassScheduleSchema.index({ school_id: 1, schedule_type: 1 });
ClassScheduleSchema.index({ school_id: 1, academic_year: 1 });
ClassScheduleSchema.index({ exam_period_id: 1 });
ClassScheduleSchema.index({ priority: -1 });

// Validation: exam_period_id is required for Exam schedule_type
ClassScheduleSchema.pre('save', function(next) {
  if (this.schedule_type === 'Exam' && !this.exam_period_id) {
    next(new Error('exam_period_id is required for Exam schedule type'));
  } else {
    next();
  }
});

// Static method to check for conflicts with priority handling
ClassScheduleSchema.statics.checkConflictWithPriority = async function(schoolId, classId, periodId, dayOfWeek, scheduleType, excludeId = null) {
  const query = {
    school_id: schoolId,
    class_id: classId,
    period_id: periodId,
    day_of_week: dayOfWeek,
    status: 'active'
  };

  if (excludeId) {
    query._id = { $ne: excludeId };
  }

  const existingSchedules = await this.find(query)
    .populate('class_id', 'name')
    .populate('subject_id', 'name')
    .populate('teacher_id', 'first_name last_name name')
    .populate('period_id', 'period_number start_time end_time')
    .populate('exam_period_id', 'name exam_type priority')
    .sort({ priority: -1 });

  if (existingSchedules.length === 0) {
    return { hasConflict: false, canReplace: false, existingSchedule: null };
  }

  const highestPrioritySchedule = existingSchedules[0];
  const newPriority = scheduleType === 'Exam' ? 100 : 50;

  // If new schedule has higher priority, it can replace existing
  const canReplace = newPriority > highestPrioritySchedule.priority;

  return {
    hasConflict: true,
    canReplace: canReplace,
    existingSchedule: highestPrioritySchedule,
    conflictType: highestPrioritySchedule.schedule_type === scheduleType ? 'same_type' : 'different_type',
    priorityConflict: !canReplace
  };
};

// Static method to get schedules by exam period
ClassScheduleSchema.statics.getByExamPeriod = async function(examPeriodId) {
  return await this.find({ exam_period_id: examPeriodId })
    .populate('class_id', 'name grade_level')
    .populate('subject_id', 'name')
    .populate('teacher_id', 'first_name last_name name')
    .populate('period_id', 'period_number start_time end_time')
    .sort({ day_of_week: 1, 'period_id.period_number': 1 });
};

// Static method to suspend normal classes during exam period
ClassScheduleSchema.statics.suspendNormalClassesDuringExam = async function(schoolId, examPeriodId) {
  const result = await this.updateMany(
    {
      school_id: schoolId,
      schedule_type: 'Normal',
      status: 'active'
      // We could add date range filtering here if needed
    },
    {
      $set: {
        status: 'suspended',
        notes: `Suspended due to exam period: ${examPeriodId}`
      }
    }
  );

  return result;
};

// Static method to reactivate suspended normal classes
ClassScheduleSchema.statics.reactivateNormalClasses = async function(schoolId, examPeriodId) {
  const result = await this.updateMany(
    {
      school_id: schoolId,
      schedule_type: 'Normal',
      status: 'suspended',
      notes: { $regex: examPeriodId }
    },
    {
      $set: {
        status: 'active',
        $unset: { notes: 1 }
      }
    }
  );

  return result;
};

const ClassSchedule = mongoose.models.ClassSchedule || mongoose.model('ClassSchedule', ClassScheduleSchema);
module.exports = ClassSchedule;