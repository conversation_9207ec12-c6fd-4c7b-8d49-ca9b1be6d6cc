const AcademicYear = require('../models/AcademicYear');

const createNextAcademicYear = async () => {
  try {
    const currentMonth = new Date().getMonth(); // 0 = Jan, 6 = July
    if (currentMonth !== 6) {
      console.log("📅 Academic year generation only runs in July.");
      return;
    }

    // Get the most recent academic year by end_date
    let lastAcademicYear = await AcademicYear.findOne().sort({ end_date: -1 });

    // If no academic year exists, seed with the first year
    if (!lastAcademicYear) {
      const seedYear = new AcademicYear({
          academic_year: "2024/2025",
          start_date: new Date("2024-09-01"),
          end_date: new Date("2025-06-30"),
      });
      await seedYear.save();
      console.log("✅ Seeded first academic year:", seedYear.academic_year);
      lastAcademicYear = seedYear;
    }
 
    // Get today's date
    const today = new Date();
    const currentYear = today.getFullYear();
    const currentMonthToday = today.getMonth();

    // Determine what academic year we should be in now
    const isSummer = currentMonthToday < 6; // Jan–June
    const targetStartYear = isSummer ? currentYear - 1 : currentYear;
    const targetEndYear = targetStartYear + 1;

    const targetAcademicYear = `${targetStartYear}/${targetEndYear}`;

    // Parse the most recent year from DB
    let [startYear, endYear] = lastAcademicYear.academic_year
      .split('/')
      .map(Number);

    // Loop to fill in missing academic years
    while (`${startYear}/${endYear}` < targetAcademicYear) {
      startYear += 1;
      endYear += 1;
      const nextAcademicYear = `${startYear}/${endYear}`;

      // Check if already exists, skip if so (defensive check)
      const exists = await AcademicYear.findOne({ academic_year: nextAcademicYear });
      if (exists) {
        console.log(`⏭️ Skipping existing academic year: ${nextAcademicYear}`);
        continue;
      }

      const newAcademicYear = new AcademicYear({
        academic_year: nextAcademicYear,
        start_date: new Date(`${startYear}-09-01`),
        end_date: new Date(`${endYear}-06-30`)
      });

      await newAcademicYear.save();
      console.log(`📘 Created academic year: ${nextAcademicYear}`);
    }

    console.log("✅ Academic years are now up to date.");
  } catch (error) {
    console.error("❌ Error creating academic years:", error);
  }
};

module.exports = { createNextAcademicYear };
