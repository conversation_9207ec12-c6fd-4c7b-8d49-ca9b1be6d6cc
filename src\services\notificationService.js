const Notification = require('../models/Notification');
const User = require('../models/User');
const School = require('../models/School');
const Student = require('../models/Student');

class NotificationService {
  // Generate unique notification ID
  static generateNotificationId(prefix = 'NOT') {
    return `${prefix}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  // Create a single notification
  static async createNotification(data) {
    try {
      const notification = new Notification({
        ...data,
        notification_id: this.generateNotificationId(),
        sent_at: new Date()
      });

      return await notification.save();
    } catch (error) {
      console.error('Error creating notification:', error);
      throw error;
    }
  }

  // Create bulk notifications
  static async createBulkNotifications(recipients, notificationData) {
    try {
      return await Notification.createBulkNotification(recipients, notificationData);
    } catch (error) {
      console.error('Error creating bulk notifications:', error);
      throw error;
    }
  }

  // Send notification to all users in a school
  static async notifySchool(schoolId, notificationData) {
    try {
      const users = await User.find({ school_id: schoolId });
      const userIds = users.map(user => user._id);
      
      return await this.createBulkNotifications(userIds, {
        ...notificationData,
        school_id: schoolId,
        recipient_type: 'school'
      });
    } catch (error) {
      console.error('Error notifying school:', error);
      throw error;
    }
  }

  // Send notification to all parents of students in a class
  static async notifyClassParents(classId, schoolId, notificationData) {
    try {
      const students = await Student.find({ class_id: classId, school_id: schoolId });
      const parentIds = students.map(student => student.parent_id).filter(Boolean);
      
      if (parentIds.length === 0) return [];
      
      return await this.createBulkNotifications(parentIds, {
        ...notificationData,
        school_id: schoolId,
        recipient_type: 'class',
        related_entity: {
          entity_type: 'class',
          entity_id: classId
        }
      });
    } catch (error) {
      console.error('Error notifying class parents:', error);
      throw error;
    }
  }

  // Send notification to all teachers in a school
  static async notifyTeachers(schoolId, notificationData) {
    try {
      const teachers = await User.find({ 
        school_id: schoolId, 
        role: { $in: ['teacher', 'dean_of_studies'] }
      });
      const teacherIds = teachers.map(teacher => teacher._id);
      
      if (teacherIds.length === 0) return [];
      
      return await this.createBulkNotifications(teacherIds, {
        ...notificationData,
        school_id: schoolId,
        recipient_type: 'school'
      });
    } catch (error) {
      console.error('Error notifying teachers:', error);
      throw error;
    }
  }

  // Send notification to all staff members in a school
  static async notifyStaff(schoolId, notificationData) {
    try {
      const staff = await User.find({ 
        school_id: schoolId, 
        role: { $in: ['school_admin', 'bursar', 'dean_of_studies', 'teacher'] }
      });
      const staffIds = staff.map(member => member._id);
      
      if (staffIds.length === 0) return [];
      
      return await this.createBulkNotifications(staffIds, {
        ...notificationData,
        school_id: schoolId,
        recipient_type: 'school'
      });
    } catch (error) {
      console.error('Error notifying staff:', error);
      throw error;
    }
  }

  // Automated notification creators for common events

  // Student enrollment notification
  static async notifyStudentEnrollment(studentId, schoolId, senderId) {
    try {
      const student = await Student.findById(studentId).populate('parent_id');
      const school = await School.findById(schoolId);
      
      const notifications = [];
      
      // Notify parent
      if (student.parent_id) {
        notifications.push(await this.createNotification({
          recipient_id: student.parent_id._id,
          school_id: schoolId,
          type: 'success',
          category: 'student',
          title: 'Student Enrollment Confirmed',
          message: `${student.first_name} ${student.last_name} has been successfully enrolled at ${school.name}.`,
          sender_id: senderId,
          sender_type: 'user',
          related_entity: {
            entity_type: 'student',
            entity_id: studentId
          },
          channels: {
            in_app: true,
            email: true
          }
        }));
      }
      
      // Notify school admin
      const admins = await User.find({ 
        school_id: schoolId, 
        role: { $in: ['school_admin', 'dean_of_studies'] }
      });
      
      for (const admin of admins) {
        notifications.push(await this.createNotification({
          recipient_id: admin._id,
          school_id: schoolId,
          type: 'info',
          category: 'student',
          title: 'New Student Enrolled',
          message: `${student.first_name} ${student.last_name} has been enrolled in the school.`,
          sender_id: senderId,
          sender_type: 'user',
          related_entity: {
            entity_type: 'student',
            entity_id: studentId
          },
          action_url: `/school-admin/students/${studentId}`,
          action_label: 'View Student'
        }));
      }
      
      return notifications;
    } catch (error) {
      console.error('Error creating student enrollment notifications:', error);
      throw error;
    }
  }

  // Grade posted notification
  static async notifyGradePosted(gradeId, studentId, subjectId, teacherId) {
    try {
      const student = await Student.findById(studentId).populate('parent_id school_id');
      
      if (!student || !student.parent_id) return [];
      
      const notifications = [];
      
      // Notify parent
      notifications.push(await this.createNotification({
        recipient_id: student.parent_id._id,
        school_id: student.school_id._id,
        type: 'info',
        category: 'grades',
        title: 'New Grade Posted',
        message: `A new grade has been posted for ${student.first_name} ${student.last_name}.`,
        sender_id: teacherId,
        sender_type: 'user',
        related_entity: {
          entity_type: 'grade',
          entity_id: gradeId
        },
        action_url: `/parent/grades/${studentId}`,
        action_label: 'View Grades',
        channels: {
          in_app: true,
          email: true
        }
      }));
      
      return notifications;
    } catch (error) {
      console.error('Error creating grade posted notifications:', error);
      throw error;
    }
  }

  // Attendance alert notification
  static async notifyAttendanceAlert(studentId, attendanceData) {
    try {
      const student = await Student.findById(studentId).populate('parent_id school_id');
      
      if (!student || !student.parent_id) return [];
      
      return await this.createNotification({
        recipient_id: student.parent_id._id,
        school_id: student.school_id._id,
        type: 'warning',
        category: 'attendance',
        title: 'Attendance Alert',
        message: `${student.first_name} ${student.last_name} was marked absent today.`,
        related_entity: {
          entity_type: 'attendance',
          entity_id: attendanceData._id
        },
        action_url: `/parent/attendance/${studentId}`,
        action_label: 'View Attendance',
        channels: {
          in_app: true,
          email: true,
          sms: true
        },
        priority: 'high'
      });
    } catch (error) {
      console.error('Error creating attendance alert notification:', error);
      throw error;
    }
  }

  // Payment reminder notification
  static async notifyPaymentReminder(studentId, feeAmount, dueDate) {
    try {
      const student = await Student.findById(studentId).populate('parent_id school_id');
      
      if (!student || !student.parent_id) return [];
      
      return await this.createNotification({
        recipient_id: student.parent_id._id,
        school_id: student.school_id._id,
        type: 'warning',
        category: 'payment',
        title: 'Payment Reminder',
        message: `School fee payment of $${feeAmount} is due on ${new Date(dueDate).toLocaleDateString()}.`,
        related_entity: {
          entity_type: 'payment',
          entity_id: studentId
        },
        action_url: `/parent/payments`,
        action_label: 'Make Payment',
        channels: {
          in_app: true,
          email: true,
          sms: true
        },
        priority: 'high',
        expires_at: new Date(dueDate)
      });
    } catch (error) {
      console.error('Error creating payment reminder notification:', error);
      throw error;
    }
  }

  // Announcement notification
  static async notifyAnnouncement(announcementId, schoolId, senderId) {
    try {
      const announcement = await require('../models/Announcement').findById(announcementId);

      // Notify all users in the school
      return await this.notifySchool(schoolId, {
        type: 'announcement',
        category: 'announcement',
        title: 'New Announcement',
        message: announcement.title,
        sender_id: senderId,
        sender_type: 'user',
        related_entity: {
          entity_type: 'announcement',
          entity_id: announcementId
        },
        action_url: `/announcements/${announcementId}`,
        action_label: 'Read Announcement',
        channels: {
          in_app: true,
          email: true
        }
      });
    } catch (error) {
      console.error('Error creating announcement notifications:', error);
      throw error;
    }
  }

  // Exam supervision assignment notification
  static async notifyExamSupervision(supervisorId, examDetails, schoolId, assignedBy) {
    try {
      const supervisor = await require('../models/User').findById(supervisorId);
      const school = await require('../models/School').findById(schoolId);

      if (!supervisor || !school) {
        throw new Error('Supervisor or school not found');
      }

      // Format exam time
      const examDate = new Date(examDetails.date || Date.now()).toLocaleDateString();
      const examTime = examDetails.period ?
        `${examDetails.period.start_time} - ${examDetails.period.end_time}` :
        'Time TBD';

      const supervisorName = supervisor.first_name && supervisor.last_name
        ? `${supervisor.first_name} ${supervisor.last_name}`
        : supervisor.name;

      return await this.createNotification({
        recipient_id: supervisorId,
        school_id: schoolId,
        type: 'info',
        category: 'schedule',
        title: 'Exam Supervision Assignment 📝',
        message: `You have been assigned to supervise the ${examDetails.subject_name} exam for ${examDetails.class_name} on ${examDate} at ${examTime}.`,
        sender_id: assignedBy,
        sender_type: 'user',
        related_entity: {
          entity_type: 'schedule',
          entity_id: examDetails.schedule_id
        },
        action_url: `/teacher-dashboard/schedule`,
        action_label: 'View Schedule',
        priority: 'high',
        channels: {
          in_app: true,
          email: true
        },
        metadata: {
          exam_details: {
            subject: examDetails.subject_name,
            class: examDetails.class_name,
            date: examDate,
            time: examTime,
            day_of_week: examDetails.day_of_week,
            schedule_type: 'Exam'
          }
        }
      });
    } catch (error) {
      console.error('Error creating exam supervision notification:', error);
      throw error;
    }
  }

  // System maintenance notification
  static async notifySystemMaintenance(maintenanceDate, duration) {
    try {
      const allUsers = await User.find({});
      const userIds = allUsers.map(user => user._id);
      
      return await this.createBulkNotifications(userIds, {
        type: 'warning',
        category: 'system',
        title: 'Scheduled Maintenance',
        message: `System maintenance is scheduled for ${new Date(maintenanceDate).toLocaleDateString()} and will last approximately ${duration}.`,
        sender_type: 'system',
        priority: 'high',
        channels: {
          in_app: true,
          email: true
        },
        scheduled_for: new Date(maintenanceDate)
      });
    } catch (error) {
      console.error('Error creating system maintenance notifications:', error);
      throw error;
    }
  }
}

module.exports = NotificationService;
