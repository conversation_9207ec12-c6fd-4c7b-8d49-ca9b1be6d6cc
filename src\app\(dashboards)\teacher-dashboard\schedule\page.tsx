"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { motion } from "framer-motion";
import { 
  Calendar, 
  Clock, 
  BookOpen, 
  Users,
  ChevronLeft,
  ChevronRight,
  Download,
  Filter
} from "lucide-react";
import { TeacherLayout } from "@/components/Dashboard/Layouts/TeacherLayout";
import { ProtectedRoute } from "@/components/Auth/ProtectedRoute";
import { useAuth } from "@/hooks/useAuth";
import { CircularLoader } from "@/components/ui/CircularLoader";
import { getTeacherNavigationItems } from "@/app/services/TeacherPermissionServices";

interface ScheduleItem {
  day: string;
  time: string;
  period: string;
  subject: string;
  class_id: string;
  class_name: string;
}

export default function TeacherSchedulePage() {
  const router = useRouter();
  const { logout } = useAuth();

  const [schedule, setSchedule] = useState<ScheduleItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedSchool, setSelectedSchool] = useState<any>(null);
  const [navigation, setNavigation] = useState<any[]>([]);
  const [currentWeek, setCurrentWeek] = useState(new Date());
  const [selectedDay, setSelectedDay] = useState<string | null>(null);

  const daysOfWeek = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday'];
  const timeSlots = [
    '08:00-09:00',
    '09:00-10:00', 
    '10:00-11:00',
    '11:00-12:00',
    '12:00-13:00', // Lunch break
    '14:00-15:00',
    '15:00-16:00',
    '16:00-17:00'
  ];

  useEffect(() => {
    const schoolData = localStorage.getItem("teacher_selected_school");
    if (schoolData) {
      const school = JSON.parse(schoolData);
      setSelectedSchool(school);
      fetchSchedule(school.school_id);
    } else {
      router.push("/teacher-dashboard");
    }
  }, [router]);

  const fetchSchedule = async (schoolId: string) => {
    try {
      setLoading(true);

      const { getTeacherPermissions, getTeacherSchedule } = await import("@/app/services/TeacherPermissionServices");
      const [teacherData, scheduleData] = await Promise.all([
        getTeacherPermissions(schoolId),
        getTeacherSchedule(schoolId)
      ]);

      // Set navigation
      setNavigation(getTeacherNavigationItems(teacherData.permissions));
      setSchedule(scheduleData);

    } catch (error) {
      console.error("Error fetching schedule:", error);
      setSchedule([]);
    } finally {
      setLoading(false);
    }
  };

  const handleSchoolChange = () => {
    localStorage.removeItem("teacher_selected_school");
    router.push("/teacher-dashboard");
  };

  const getScheduleForDayAndTime = (day: string, time: string) => {
    return schedule.find(item => item.day === day && item.time === time);
  };

  const getSubjectColor = (subject: string) => {
    const colors = {
      Mathematics: "bg-blue-100 text-blue-800 border-blue-200 dark:bg-blue-900/30 dark:text-blue-300 dark:border-blue-800",
      Physics: "bg-purple-100 text-purple-800 border-purple-200 dark:bg-purple-900/30 dark:text-purple-300 dark:border-purple-800",
      Chemistry: "bg-green-100 text-green-800 border-green-200 dark:bg-green-900/30 dark:text-green-300 dark:border-green-800",
      Biology: "bg-orange-100 text-orange-800 border-orange-200 dark:bg-orange-900/30 dark:text-orange-300 dark:border-orange-800",
      English: "bg-red-100 text-red-800 border-red-200 dark:bg-red-900/30 dark:text-red-300 dark:border-red-800",
      History: "bg-yellow-100 text-yellow-800 border-yellow-200 dark:bg-yellow-900/30 dark:text-yellow-300 dark:border-yellow-800"
    };
    return colors[subject as keyof typeof colors] || "bg-gray-100 text-gray-800 border-gray-200 dark:bg-gray-900/30 dark:text-gray-300 dark:border-gray-800";
  };

  const getTodaySchedule = () => {
    const today = new Date().toLocaleDateString('en-US', { weekday: 'long' });
    return schedule.filter(item => item.day === today);
  };

  const getWeekDates = () => {
    const startOfWeek = new Date(currentWeek);
    const day = startOfWeek.getDay();
    const diff = startOfWeek.getDate() - day + (day === 0 ? -6 : 1); // Adjust when day is Sunday
    startOfWeek.setDate(diff);

    return daysOfWeek.map((_, index) => {
      const date = new Date(startOfWeek);
      date.setDate(startOfWeek.getDate() + index);
      return date;
    });
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <CircularLoader size={32} color="teal" />
      </div>
    );
  }

  return (
    <ProtectedRoute allowedRoles={["teacher"]}>
      <TeacherLayout
        navigation={navigation}
        selectedSchool={selectedSchool ? {
          _id: selectedSchool.school_id,
          name: selectedSchool.school_name
        } : null}
        onSchoolChange={handleSchoolChange}
        onLogout={logout}
      >
        <div className="space-y-6">
          {/* Header */}
          <div className="bg-widget rounded-lg border border-stroke p-6">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <div className="w-12 h-12 bg-purple-500 rounded-full flex items-center justify-center">
                  <Calendar className="h-6 w-6 text-white" />
                </div>
                <div>
                  <h1 className="text-2xl font-bold text-foreground">My Schedule</h1>
                  <p className="text-foreground/60">
                    Weekly timetable at {selectedSchool?.school_name}
                  </p>
                </div>
              </div>
              
              <div className="flex items-center space-x-2">
                <button className="px-4 py-2 border border-stroke rounded-md hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors flex items-center space-x-2">
                  <Filter className="h-4 w-4" />
                  <span>Filter</span>
                </button>
                <button className="px-4 py-2 bg-teal text-white rounded-md hover:bg-teal-600 transition-colors flex items-center space-x-2">
                  <Download className="h-4 w-4" />
                  <span>Export</span>
                </button>
              </div>
            </div>
          </div>

          {/* Week Navigation */}
          <div className="bg-widget rounded-lg border border-stroke p-4">
            <div className="flex items-center justify-between">
              <button
                onClick={() => {
                  const prevWeek = new Date(currentWeek);
                  prevWeek.setDate(prevWeek.getDate() - 7);
                  setCurrentWeek(prevWeek);
                }}
                className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-md transition-colors"
              >
                <ChevronLeft className="h-5 w-5" />
              </button>
              
              <div className="text-center">
                <h3 className="text-lg font-semibold text-foreground">
                  Week of {getWeekDates()[0].toLocaleDateString('en-US', { month: 'long', day: 'numeric' })} - {getWeekDates()[4].toLocaleDateString('en-US', { month: 'long', day: 'numeric', year: 'numeric' })}
                </h3>
              </div>
              
              <button
                onClick={() => {
                  const nextWeek = new Date(currentWeek);
                  nextWeek.setDate(nextWeek.getDate() + 7);
                  setCurrentWeek(nextWeek);
                }}
                className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-md transition-colors"
              >
                <ChevronRight className="h-5 w-5" />
              </button>
            </div>
          </div>

          {/* Today's Schedule Quick View */}
          <div className="bg-widget rounded-lg border border-stroke p-6">
            <h3 className="text-lg font-semibold text-foreground mb-4">Today's Classes</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {getTodaySchedule().length > 0 ? (
                getTodaySchedule().map((item, index) => (
                  <motion.div
                    key={index}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: index * 0.1 }}
                    className={`p-4 rounded-lg border-2 ${getSubjectColor(item.subject)}`}
                  >
                    <div className="flex items-center justify-between mb-2">
                      <span className="font-semibold">{item.subject}</span>
                      <span className="text-sm">Period {item.period}</span>
                    </div>
                    <div className="flex items-center space-x-2 text-sm mb-1">
                      <Clock className="h-4 w-4" />
                      <span>{item.time}</span>
                    </div>
                    <div className="flex items-center space-x-2 text-sm">
                      <Users className="h-4 w-4" />
                      <span>{item.class_name}</span>
                    </div>
                  </motion.div>
                ))
              ) : (
                <div className="col-span-full text-center py-8">
                  <Calendar className="h-12 w-12 text-foreground/30 mx-auto mb-2" />
                  <p className="text-foreground/60">No classes scheduled for today</p>
                </div>
              )}
            </div>
          </div>

          {/* Weekly Schedule Grid */}
          <div className="bg-widget rounded-lg border border-stroke p-6">
            <h3 className="text-lg font-semibold text-foreground mb-4">Weekly Schedule</h3>
            <div className="overflow-x-auto">
              <table className="w-full border-collapse">
                <thead>
                  <tr>
                    <th className="border border-stroke p-3 bg-gray-50 dark:bg-gray-800 text-left font-medium text-foreground">
                      Time
                    </th>
                    {daysOfWeek.map((day, index) => (
                      <th key={day} className="border border-stroke p-3 bg-gray-50 dark:bg-gray-800 text-center font-medium text-foreground min-w-[150px]">
                        <div>
                          <div>{day}</div>
                          <div className="text-xs text-foreground/60 font-normal">
                            {getWeekDates()[index].toLocaleDateString('en-US', { month: 'short', day: 'numeric' })}
                          </div>
                        </div>
                      </th>
                    ))}
                  </tr>
                </thead>
                <tbody>
                  {timeSlots.map((time) => (
                    <tr key={time}>
                      <td className="border border-stroke p-3 font-medium text-foreground bg-gray-50 dark:bg-gray-800">
                        {time}
                      </td>
                      {daysOfWeek.map((day) => {
                        const scheduleItem = getScheduleForDayAndTime(day, time);
                        return (
                          <td key={`${day}-${time}`} className="border border-stroke p-2">
                            {scheduleItem ? (
                              <div className={`p-2 rounded-md text-sm ${getSubjectColor(scheduleItem.subject)}`}>
                                <div className="font-medium">{scheduleItem.subject}</div>
                                <div className="text-xs">{scheduleItem.class_name}</div>
                                <div className="text-xs">Period {scheduleItem.period}</div>
                              </div>
                            ) : time === '12:00-13:00' ? (
                              <div className="p-2 text-center text-foreground/50 text-sm">
                                Lunch Break
                              </div>
                            ) : (
                              <div className="p-2 text-center text-foreground/30 text-sm">
                                Free
                              </div>
                            )}
                          </td>
                        );
                      })}
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>

          {/* Schedule Statistics */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            <div className="bg-widget rounded-lg border border-stroke p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-foreground/60">Total Classes</p>
                  <p className="text-2xl font-bold text-foreground">{schedule.length}</p>
                </div>
                <div className="w-10 h-10 bg-blue-500 rounded-lg flex items-center justify-center">
                  <BookOpen className="h-5 w-5 text-white" />
                </div>
              </div>
            </div>

            <div className="bg-widget rounded-lg border border-stroke p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-foreground/60">Today's Classes</p>
                  <p className="text-2xl font-bold text-foreground">{getTodaySchedule().length}</p>
                </div>
                <div className="w-10 h-10 bg-green-500 rounded-lg flex items-center justify-center">
                  <Calendar className="h-5 w-5 text-white" />
                </div>
              </div>
            </div>

            <div className="bg-widget rounded-lg border border-stroke p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-foreground/60">Subjects</p>
                  <p className="text-2xl font-bold text-foreground">
                    {new Set(schedule.map(item => item.subject)).size}
                  </p>
                </div>
                <div className="w-10 h-10 bg-purple-500 rounded-lg flex items-center justify-center">
                  <BookOpen className="h-5 w-5 text-white" />
                </div>
              </div>
            </div>

            <div className="bg-widget rounded-lg border border-stroke p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-foreground/60">Classes</p>
                  <p className="text-2xl font-bold text-foreground">
                    {new Set(schedule.map(item => item.class_name)).size}
                  </p>
                </div>
                <div className="w-10 h-10 bg-orange-500 rounded-lg flex items-center justify-center">
                  <Users className="h-5 w-5 text-white" />
                </div>
              </div>
            </div>
          </div>
        </div>
      </TeacherLayout>
    </ProtectedRoute>
  );
}
