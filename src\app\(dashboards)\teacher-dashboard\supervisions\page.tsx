"use client";

import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { Eye, Calendar, Clock, BookOpen, Users, AlertCircle, RefreshCw } from "lucide-react";
import TeacherLayout from "@/components/Dashboard/Layouts/TeacherLayout";
import useAuth from "@/app/hooks/useAuth";
import ProtectedRoute from "@/components/utils/ProtectedRoute";
import CircularLoader from "@/components/widgets/CircularLoader";
import { motion } from "framer-motion";
import { getTeacherPermissions, getTeacherNavigationItems } from "@/app/services/TeacherPermissionServices";
import { getTeacherSupervisions, ScheduleEntry } from "@/app/services/TimetableServices";

interface SelectedSchool {
  school_id: string;
  school_name: string;
  access_granted_at: string;
}

interface SupervisionData {
  supervisions: ScheduleEntry[];
  total: number;
  message: string;
}

const navigation = {
  icon: Eye,
  baseHref: "/teacher-dashboard/supervisions",
  title: "Exam Supervisions"
};

export default function TeacherSupervisionsPage() {
  const { user, logout } = useAuth();
  const router = useRouter();
  
  const [selectedSchool, setSelectedSchool] = useState<SelectedSchool | null>(null);
  const [loading, setLoading] = useState(true);
  const [supervisions, setSupervisions] = useState<ScheduleEntry[]>([]);
  const [navigationItems, setNavigationItems] = useState<any[]>([]);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    // Check if user is a teacher
    if (user && user.role !== "teacher") {
      router.push("/dashboard");
      return;
    }

    // Get selected school from localStorage
    const storedSchool = localStorage.getItem("teacher_selected_school");
    if (storedSchool) {
      try {
        const school = JSON.parse(storedSchool);
        setSelectedSchool(school);
        fetchData(school.school_id);
      } catch (error) {
        console.error("Error parsing stored school:", error);
        router.push("/teacher-dashboard");
      }
    } else {
      router.push("/teacher-dashboard");
    }
  }, [user, router]);

  const fetchData = async (schoolId: string) => {
    if (!user?._id) return;

    try {
      setLoading(true);
      setError(null);

      // Fetch teacher permissions and supervisions in parallel
      const [teacherData, supervisionsData] = await Promise.all([
        getTeacherPermissions(schoolId),
        getTeacherSupervisions(schoolId, user._id)
      ]);

      // Set navigation
      setNavigationItems(getTeacherNavigationItems(teacherData.permissions));
      
      // Set supervisions
      setSupervisions(supervisionsData.supervisions || []);

    } catch (error) {
      console.error("Error fetching data:", error);
      setError("Failed to load supervisions data");
    } finally {
      setLoading(false);
    }
  };

  const handleSchoolChange = () => {
    localStorage.removeItem("teacher_selected_school");
    router.push("/teacher-dashboard");
  };

  const formatTime = (startTime: string, endTime: string) => {
    return `${startTime} - ${endTime}`;
  };

  const getDayColor = (day: string) => {
    const colors: Record<string, string> = {
      'Monday': 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200',
      'Tuesday': 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200',
      'Wednesday': 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200',
      'Thursday': 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200',
      'Friday': 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200',
    };
    return colors[day] || 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';
  };

  const groupSupervisionsByDay = (supervisions: ScheduleEntry[]) => {
    const days = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday'];
    const grouped: Record<string, ScheduleEntry[]> = {};
    
    days.forEach(day => {
      grouped[day] = supervisions.filter(s => s.day_of_week === day);
    });
    
    return grouped;
  };

  if (loading) {
    return (
      <ProtectedRoute allowedRoles={["teacher"]}>
        <TeacherLayout
          navigation={navigation}
          selectedSchool={selectedSchool ? {
            _id: selectedSchool.school_id,
            name: selectedSchool.school_name
          } : null}
          onSchoolChange={handleSchoolChange}
          onLogout={logout}
        >
          <div className="flex items-center justify-center min-h-96">
            <CircularLoader />
          </div>
        </TeacherLayout>
      </ProtectedRoute>
    );
  }

  const groupedSupervisions = groupSupervisionsByDay(supervisions);

  return (
    <ProtectedRoute allowedRoles={["teacher"]}>
      <TeacherLayout
        navigation={navigation}
        selectedSchool={selectedSchool ? {
          _id: selectedSchool.school_id,
          name: selectedSchool.school_name
        } : null}
        onSchoolChange={handleSchoolChange}
        onLogout={logout}
      >
        <div className="space-y-6">
          {/* Header */}
          <div className="bg-widget rounded-lg border border-stroke p-6">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <div className="w-12 h-12 bg-orange-100 dark:bg-orange-900/30 rounded-full flex items-center justify-center">
                  <Eye className="h-6 w-6 text-orange-600 dark:text-orange-400" />
                </div>
                <div>
                  <h1 className="text-2xl font-bold text-foreground">Exam Supervisions</h1>
                  <p className="text-foreground/60">
                    {supervisions.length} supervision{supervisions.length !== 1 ? 's' : ''} assigned for {selectedSchool?.school_name}
                  </p>
                </div>
              </div>
              
              <button
                onClick={() => selectedSchool && fetchData(selectedSchool.school_id)}
                className="flex items-center space-x-2 px-4 py-2 bg-teal text-white rounded-lg hover:bg-teal-600 transition-colors"
              >
                <RefreshCw className="h-4 w-4" />
                <span>Refresh</span>
              </button>
            </div>
          </div>

          {/* Error State */}
          {error && (
            <div className="bg-red-50 border border-red-200 rounded-lg p-4">
              <div className="flex items-center space-x-2">
                <AlertCircle className="h-5 w-5 text-red-500" />
                <p className="text-red-700">{error}</p>
              </div>
            </div>
          )}

          {/* Content */}
          {supervisions.length === 0 ? (
            <div className="bg-widget rounded-lg border border-stroke p-12">
              <div className="text-center">
                <Eye className="h-16 w-16 text-foreground/30 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-foreground mb-2">No Exam Supervisions</h3>
                <p className="text-foreground/60 mb-4">
                  You currently have no exam supervisions assigned.
                </p>
                <p className="text-sm text-foreground/50">
                  You will be notified when exam supervisions are assigned to you by the school administration.
                </p>
              </div>
            </div>
          ) : (
            <div className="space-y-6">
              {Object.entries(groupedSupervisions).map(([day, daySupervisions]) => (
                daySupervisions.length > 0 && (
                  <motion.div
                    key={day}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    className="bg-widget rounded-lg border border-stroke overflow-hidden"
                  >
                    <div className="bg-gray-50 dark:bg-gray-800 px-6 py-3 border-b border-stroke">
                      <h3 className="font-semibold text-foreground flex items-center space-x-2">
                        <Calendar className="h-4 w-4" />
                        <span>{day}</span>
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${getDayColor(day)}`}>
                          {daySupervisions.length} supervision{daySupervisions.length !== 1 ? 's' : ''}
                        </span>
                      </h3>
                    </div>
                    
                    <div className="p-6">
                      <div className="space-y-4">
                        {daySupervisions.map((supervision, index) => (
                          <motion.div
                            key={supervision._id}
                            initial={{ opacity: 0, x: -20 }}
                            animate={{ opacity: 1, x: 0 }}
                            transition={{ delay: index * 0.1 }}
                            className="border-l-4 border-l-orange-500 bg-orange-50 dark:bg-orange-900/20 rounded-lg p-4"
                          >
                            <div className="flex items-start justify-between">
                              <div className="flex-1">
                                <div className="flex items-center space-x-2 mb-2">
                                  <BookOpen className="h-4 w-4 text-foreground/60" />
                                  <span className="font-medium text-foreground text-lg">
                                    {supervision.subject_name}
                                  </span>
                                  <span className="px-2 py-1 bg-orange-100 text-orange-800 dark:bg-orange-900/30 dark:text-orange-300 text-xs font-medium rounded-full">
                                    Exam Supervision
                                  </span>
                                </div>
                                
                                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm text-foreground/70">
                                  <div className="flex items-center space-x-1">
                                    <Users className="h-3 w-3" />
                                    <span>Class: {supervision.class_name}</span>
                                  </div>
                                  
                                  <div className="flex items-center space-x-1">
                                    <Clock className="h-3 w-3" />
                                    <span>
                                      Period {supervision.period_number} • {formatTime(supervision.start_time, supervision.end_time)}
                                    </span>
                                  </div>
                                  
                                  <div className="flex items-center space-x-1">
                                    <Calendar className="h-3 w-3" />
                                    <span>{supervision.day_of_week}</span>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </motion.div>
                        ))}
                      </div>
                    </div>
                  </motion.div>
                )
              ))}
            </div>
          )}
        </div>
      </TeacherLayout>
    </ProtectedRoute>
  );
}
