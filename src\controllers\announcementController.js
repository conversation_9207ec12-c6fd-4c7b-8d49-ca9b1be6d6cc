// controllers/announcementController.js

const Announcement = require('../models/Announcement'); // Assuming you have an Announcement model
const { ensureUniqueId } = require('../utils/generateId'); 

const testAnnouncementResponse = (req, res) => {
  res.status(200).json({ message: 'Hi, this is announcement' });
};

// // Get all announcements
const getAllAnnouncements = async (req, res) => {
  try {
    const announcements = await Announcement.find();
    res.json(announcements);
  } catch (err) {
    res.status(500).json({ message: err.message });
  }
};

// // Create a new announcement
const createAnnouncement = async (req, res) => {
  try {
    const announcementId = await ensureUniqueId(Announcement, 'announcement_id', 'ANC');

    // Extract required fields from request body
    const {
      title,
      content,
      school_id,
      author_id,
      target_audience = 'all',
      priority = 'medium',
      is_published = false,
      expires_at,
      announcement // Keep for backward compatibility
    } = req.body;

    // Validate required fields
    if (!title || !content || !school_id || !author_id) {
      return res.status(400).json({
        message: 'Missing required fields: title, content, school_id, and author_id are required'
      });
    }

    // Create the new announcement object
    const newAnnouncement = new Announcement({
      announcement_id: announcementId,
      title,
      content,
      school_id,
      author_id,
      target_audience,
      priority,
      is_published,
      expires_at: expires_at ? new Date(expires_at) : undefined,
      published_at: is_published ? new Date() : undefined,
      announcement // Keep for backward compatibility
    });

    await newAnnouncement.save();

    // Populate the response with author and school info
    const populatedAnnouncement = await Announcement.findById(newAnnouncement._id)
      .populate('author_id', 'first_name last_name email')
      .populate('school_id', 'name');

    res.status(201).json(populatedAnnouncement);
  } catch (err) {
    res.status(400).json({ message: err.message });
  }
};

// // Get an announcement by ID
const getAnnouncementById = async (req, res) => {
  try {
    const announcement = await Announcement.findOne({announcement_id:req.params.id});
    if (!announcement) {
      return res.status(404).json({ message: 'Announcement not found' });
    }
    res.json(announcement);
  } catch (err) {
    res.status(500).json({ message: err.message });
  }
};

// // Update announcement by ID
const updateAnnouncementById = async (req, res) => {
  try {
    const {
      title,
      content,
      target_audience,
      priority,
      is_published,
      expires_at,
      announcement // Keep for backward compatibility
    } = req.body;

    // Prepare update data
    const updateData = {};
    if (title !== undefined) updateData.title = title;
    if (content !== undefined) updateData.content = content;
    if (target_audience !== undefined) updateData.target_audience = target_audience;
    if (priority !== undefined) updateData.priority = priority;
    if (is_published !== undefined) {
      updateData.is_published = is_published;
      // Set published_at when publishing for the first time
      if (is_published) {
        updateData.published_at = new Date();
      }
    }
    if (expires_at !== undefined) updateData.expires_at = expires_at ? new Date(expires_at) : null;
    if (announcement !== undefined) updateData.announcement = announcement; // Backward compatibility

    const updatedAnnouncement = await Announcement.findOneAndUpdate(
      { announcement_id: req.params.id },
      updateData,
      { new: true }
    ).populate('author_id', 'first_name last_name email')
     .populate('school_id', 'name');

    if (!updatedAnnouncement) {
      return res.status(404).json({ message: 'Announcement not found' });
    }

    res.json(updatedAnnouncement);
  } catch (err) {
    res.status(400).json({ message: err.message });
  }
};

// // Delete announcement by ID
const deleteAnnouncementById = async (req, res) => {
  try {
    const deletedAnnouncement = await Announcement.findOneAndDelete({announcement_id:req.params.id});
    if (!deletedAnnouncement) {
      return res.status(404).json({ message: 'Announcement not found' });
    }
    res.json({ message: 'Announcement deleted successfully' });
  } catch (err) {
    res.status(500).json({ message: err.message });
  }
};
// // Delete multiple announcements by IDs
const deleteMultipleAnnouncements = async (req, res) => {
  const { ids } = req.body; // Expecting an array of announcement_ids in the request body
  if (!Array.isArray(ids) || ids.length === 0) {
    return res.status(400).json({ message: 'Invalid input: ids should be a non-empty array' });
  }

  try {
    // Delete announcements where announcement_id is in the provided array of IDs
    const result = await Announcement.deleteMany({ _id: { $in: ids } });

    if (result.deletedCount === 0) {
      return res.status(404).json({ message: 'No announcements found for the provided IDs' });
    }

    res.json({ message: `${result.deletedCount} announcements deleted successfully` });
  } catch (err) {
    res.status(500).json({ message: err.message });
  }
};

// Delete ALL announcement records
const deleteAllAnnouncements = async (req, res) => {
  try {
    // First, count how many announcements exist
    const announcementCount = await Announcement.countDocuments();

    if (announcementCount === 0) {
      return res.status(404).json({ message: 'No announcements found to delete' });
    }

    // Delete all announcement records
    const result = await Announcement.deleteMany({});

    res.json({
      message: `All ${result.deletedCount} announcement records deleted successfully`,
      deletedCount: result.deletedCount
    });
  } catch (err) {
    res.status(500).json({ message: err.message });
  }
};

// Get announcements by school ID
const getAnnouncementsBySchool = async (req, res) => {
  try {
    const { school_id } = req.params;

    if (!school_id) {
      return res.status(400).json({ message: 'School ID is required' });
    }

    // Fetch announcements for the specific school
    const announcements = await Announcement.find({ school_id })
      .populate('author_id', 'first_name last_name email')
      .populate('school_id', 'name')
      .sort({ createdAt: -1 }); // Sort by creation date, newest first

    res.status(200).json(announcements);
  } catch (err) {
    res.status(500).json({ message: err.message });
  }
};

module.exports = {
  testAnnouncementResponse,
  getAllAnnouncements,
  createAnnouncement,
  getAnnouncementById,
  updateAnnouncementById,
  deleteAnnouncementById,
  deleteMultipleAnnouncements,
  deleteAllAnnouncements,
  getAnnouncementsBySchool,
};
